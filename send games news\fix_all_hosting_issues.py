#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع مشاكل الاستضافة
حل مشاكل Telegram، Supabase، وإزالة الاعتماد على ngrok
"""

import os
import sys
import json
import time
import requests
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HostingFixer:
    """فئة لإصلاح مشاكل الاستضافة"""
    
    def __init__(self):
        self.fixes_applied = []
        self.errors = []
        
    def log_fix(self, message: str):
        """تسجيل إصلاح"""
        logger.info(f"✅ {message}")
        self.fixes_applied.append(message)
        
    def log_error(self, message: str):
        """تسجيل خطأ"""
        logger.error(f"❌ {message}")
        self.errors.append(message)
        
    def fix_telegram_conflicts(self) -> bool:
        """إصلاح مشاكل Telegram getUpdates conflicts"""
        logger.info("🔧 إصلاح مشاكل Telegram...")
        
        try:
            # الحصول على token البوت
            bot_token = self._get_bot_token()
            if not bot_token:
                self.log_error("لم يتم العثور على token البوت")
                return False
                
            # مسح webhook إذا كان موجود
            if self._clear_webhook(bot_token):
                self.log_fix("تم مسح webhook للبوت")
                
            # مسح التحديثات المعلقة
            if self._clear_pending_updates(bot_token):
                self.log_fix("تم مسح التحديثات المعلقة")
                
            # إضافة معالج للتأكد من عدم تشغيل أكثر من instance
            self._create_telegram_singleton()
            self.log_fix("تم إنشاء نظام منع التشغيل المتعدد للبوت")
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إصلاح Telegram: {e}")
            return False
    
    def fix_supabase_issues(self) -> bool:
        """إصلاح مشاكل Supabase"""
        logger.info("🔧 إصلاح مشاكل Supabase...")
        
        try:
            # إعداد متغيرات Supabase
            supabase_config = {
                'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
                'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
            }
            
            # تعيين متغيرات البيئة
            for key, value in supabase_config.items():
                if not os.getenv(key):
                    os.environ[key] = value
                    self.log_fix(f"تم تعيين {key}")
                    
            # إنشاء ملف إعدادات Supabase محسن
            self._create_supabase_client()
            self.log_fix("تم إنشاء عميل Supabase محسن")
            
            # اختبار الاتصال
            if self._test_supabase_connection():
                self.log_fix("تم اختبار اتصال Supabase بنجاح")
            else:
                logger.warning("⚠️ فشل اختبار Supabase، سيتم استخدام قاعدة بيانات محلية")
                self._setup_local_database()
                self.log_fix("تم إعداد قاعدة بيانات محلية كبديل")
                
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إصلاح Supabase: {e}")
            return False
    
    def remove_ngrok_dependency(self) -> bool:
        """إزالة الاعتماد على ngrok"""
        logger.info("🔧 إزالة الاعتماد على ngrok...")
        
        try:
            # البحث عن ملفات تستخدم ngrok
            ngrok_files = self._find_ngrok_usage()
            
            for file_path in ngrok_files:
                if self._remove_ngrok_from_file(file_path):
                    self.log_fix(f"تم إزالة ngrok من {file_path}")
                    
            # إنشاء بديل لـ ngrok للاستضافة
            self._create_hosting_alternative()
            self.log_fix("تم إنشاء بديل للاستضافة بدلاً من ngrok")
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إزالة ngrok: {e}")
            return False
    
    def optimize_for_hosting(self) -> bool:
        """تحسين الإعدادات للاستضافة"""
        logger.info("🔧 تحسين الإعدادات للاستضافة...")
        
        try:
            # إعداد متغيرات البيئة للاستضافة
            hosting_env = {
                'PYTHONUNBUFFERED': '1',
                'PYTHONIOENCODING': 'utf-8',
                'ENVIRONMENT': 'production',
                'DEBUG': 'false',
                'LOG_LEVEL': 'INFO',
                'RENDER': 'true',
                'PORT': os.getenv('PORT', '10000')
            }
            
            for key, value in hosting_env.items():
                if not os.getenv(key):
                    os.environ[key] = value
                    self.log_fix(f"تم تعيين متغير الاستضافة: {key}")
                    
            # إنشاء المجلدات المطلوبة
            required_dirs = [
                'data', 'logs', 'cache', 'images', 'temp', 
                'config', 'backups', 'assets'
            ]
            
            for dir_name in required_dirs:
                Path(dir_name).mkdir(exist_ok=True)
                
            self.log_fix("تم إنشاء جميع المجلدات المطلوبة")
            
            # إنشاء ملف إعدادات محسن للاستضافة
            self._create_hosting_config()
            self.log_fix("تم إنشاء إعدادات محسنة للاستضافة")
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في تحسين الاستضافة: {e}")
            return False
    
    def _get_bot_token(self) -> Optional[str]:
        """الحصول على token البوت"""
        possible_tokens = [
            os.getenv('BOT_TOKEN'),
            os.getenv('TELEGRAM_BOT_TOKEN'),
            '**********************************************'
        ]
        
        for token in possible_tokens:
            if token and token.strip():
                return token.strip()
        return None
    
    def _clear_webhook(self, bot_token: str) -> bool:
        """مسح webhook للبوت"""
        try:
            url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
            response = requests.post(url, json={"drop_pending_updates": True}, timeout=10)
            return response.status_code == 200 and response.json().get('ok', False)
        except:
            return False
    
    def _clear_pending_updates(self, bot_token: str) -> bool:
        """مسح التحديثات المعلقة"""
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
            params = {"offset": -1, "limit": 1, "timeout": 0}
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    params['offset'] = last_update_id + 1
                    requests.get(url, params=params, timeout=10)
                return True
        except:
            pass
        return False
    
    def _create_telegram_singleton(self):
        """إنشاء نظام منع التشغيل المتعدد"""
        singleton_code = '''
import os
import fcntl
import atexit

class TelegramSingleton:
    def __init__(self):
        self.lockfile = None
        
    def acquire_lock(self):
        """الحصول على قفل لمنع التشغيل المتعدد"""
        try:
            self.lockfile = open('/tmp/telegram_bot.lock', 'w')
            fcntl.flock(self.lockfile.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            atexit.register(self.release_lock)
            return True
        except:
            return False
            
    def release_lock(self):
        """تحرير القفل"""
        if self.lockfile:
            try:
                fcntl.flock(self.lockfile.fileno(), fcntl.LOCK_UN)
                self.lockfile.close()
                os.unlink('/tmp/telegram_bot.lock')
            except:
                pass

telegram_singleton = TelegramSingleton()
'''
        
        with open('modules/telegram_singleton.py', 'w', encoding='utf-8') as f:
            f.write(singleton_code)
    
    def _test_supabase_connection(self) -> bool:
        """اختبار اتصال Supabase"""
        try:
            url = os.getenv('SUPABASE_URL')
            key = os.getenv('SUPABASE_KEY')
            
            if not url or not key:
                return False
                
            headers = {
                'apikey': key,
                'Authorization': f'Bearer {key}'
            }
            
            response = requests.get(f"{url}/rest/v1/", headers=headers, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def _setup_local_database(self):
        """إعداد قاعدة بيانات محلية"""
        db_code = '''
import sqlite3
import os
from pathlib import Path

class LocalDatabase:
    def __init__(self):
        Path("data").mkdir(exist_ok=True)
        self.db_path = "data/local_articles.db"
        self.init_database()
        
    def init_database(self):
        """إنشاء الجداول المطلوبة"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS articles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()

local_db = LocalDatabase()
'''
        
        with open('modules/local_database.py', 'w', encoding='utf-8') as f:
            f.write(db_code)
    
    def _find_ngrok_usage(self) -> List[str]:
        """البحث عن ملفات تستخدم ngrok"""
        ngrok_files = []
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if 'ngrok' in content.lower():
                                ngrok_files.append(file_path)
                    except:
                        pass
                        
        return ngrok_files
    
    def _remove_ngrok_from_file(self, file_path: str) -> bool:
        """إزالة استخدام ngrok من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # استبدال استخدامات ngrok
            replacements = [
                ('import pyngrok', '# import pyngrok  # تم إزالة ngrok'),
                ('from pyngrok', '# from pyngrok  # تم إزالة ngrok'),
                ('ngrok.connect', '# ngrok.connect  # تم إزالة ngrok'),
                ('ngrok.kill', '# ngrok.kill  # تم إزالة ngrok')
            ]
            
            modified = False
            for old, new in replacements:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
                    
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
                
        except Exception as e:
            logger.warning(f"تحذير: فشل في تعديل {file_path}: {e}")
            
        return False
    
    def _create_hosting_alternative(self):
        """إنشاء بديل للاستضافة"""
        hosting_code = '''
import os
from flask import Flask, jsonify

class HostingServer:
    def __init__(self):
        self.app = Flask(__name__)
        self.setup_routes()
        
    def setup_routes(self):
        @self.app.route('/')
        def health_check():
            return jsonify({
                "status": "healthy",
                "service": "gaming_news_bot",
                "timestamp": str(datetime.now())
            })
            
        @self.app.route('/status')
        def bot_status():
            return jsonify({
                "bot_running": True,
                "environment": os.getenv("ENVIRONMENT", "production")
            })
    
    def run(self):
        port = int(os.getenv('PORT', 10000))
        self.app.run(host='0.0.0.0', port=port, debug=False)

hosting_server = HostingServer()
'''
        
        with open('modules/hosting_server.py', 'w', encoding='utf-8') as f:
            f.write(hosting_code)
    
    def _create_hosting_config(self):
        """إنشاء إعدادات الاستضافة"""
        config = {
            "hosting": {
                "platform": "render",
                "use_ngrok": False,
                "port": int(os.getenv('PORT', 10000)),
                "health_check_enabled": True
            },
            "telegram": {
                "use_webhook": False,
                "use_polling": True,
                "drop_pending_updates": True
            },
            "database": {
                "use_supabase": True,
                "fallback_to_local": True
            }
        }
        
        with open('config/hosting_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def _create_supabase_client(self):
        """إنشاء عميل Supabase محسن"""
        client_code = '''
import os
import requests
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class SupabaseClient:
    def __init__(self):
        self.url = os.getenv('SUPABASE_URL', '')
        self.key = os.getenv('SUPABASE_KEY', '')
        self.headers = {
            'apikey': self.key,
            'Authorization': f'Bearer {self.key}',
            'Content-Type': 'application/json'
        }
        
    def is_available(self) -> bool:
        """فحص توفر Supabase"""
        if not self.url or not self.key:
            return False
            
        try:
            response = requests.get(f"{self.url}/rest/v1/", headers=self.headers, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def safe_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[Any, Any]]:
        """طلب آمن إلى Supabase"""
        if not self.is_available():
            logger.warning("Supabase غير متوفر، سيتم استخدام قاعدة البيانات المحلية")
            return None
            
        try:
            url = f"{self.url}/rest/v1/{endpoint}"
            kwargs.setdefault('headers', self.headers)
            kwargs.setdefault('timeout', 10)
            
            response = requests.request(method, url, **kwargs)
            
            if response.status_code in [200, 201]:
                return response.json()
            else:
                logger.warning(f"استجابة غير متوقعة من Supabase: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في طلب Supabase: {e}")
            return None

supabase_client = SupabaseClient()
'''
        
        os.makedirs('modules', exist_ok=True)
        with open('modules/supabase_client_fixed.py', 'w', encoding='utf-8') as f:
            f.write(client_code)

def main():
    """الدالة الرئيسية"""
    print("🚀 إصلاح شامل لمشاكل الاستضافة")
    print("=" * 60)
    
    fixer = HostingFixer()
    
    # تطبيق جميع الإصلاحات
    fixes = [
        ("إصلاح مشاكل Telegram", fixer.fix_telegram_conflicts),
        ("إصلاح مشاكل Supabase", fixer.fix_supabase_issues),
        ("إزالة الاعتماد على ngrok", fixer.remove_ngrok_dependency),
        ("تحسين إعدادات الاستضافة", fixer.optimize_for_hosting)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        print(f"\n🔧 {fix_name}...")
        try:
            if fix_func():
                success_count += 1
                print(f"✅ {fix_name} - تم بنجاح")
            else:
                print(f"❌ {fix_name} - فشل")
        except Exception as e:
            print(f"❌ {fix_name} - خطأ: {e}")
    
    # تقرير نهائي
    print("\n" + "=" * 60)
    print("📊 تقرير الإصلاحات:")
    print(f"✅ تم تطبيق: {success_count}/{len(fixes)} إصلاحات")
    
    if fixer.fixes_applied:
        print("\n🎉 الإصلاحات المطبقة:")
        for fix in fixer.fixes_applied:
            print(f"  • {fix}")
    
    if fixer.errors:
        print("\n⚠️ الأخطاء:")
        for error in fixer.errors:
            print(f"  • {error}")
    
    print("\n📋 الخطوات التالية:")
    print("1. تشغيل البوت: python main.py")
    print("2. مراقبة السجلات للتأكد من عدم وجود أخطاء")
    print("3. اختبار جميع الوظائف")
    
    return success_count == len(fixes)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
