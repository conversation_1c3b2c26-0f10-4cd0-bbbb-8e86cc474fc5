#!/usr/bin/env python3
"""
ملف تشغيل البوت على Render - نسخة Python
Render bot startup script - Python version
"""

import os
import sys
import subprocess
import logging

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """الدالة الرئيسية لتشغيل البوت"""
    logger.info("🚀 بدء تشغيل البوت على Render...")
    logger.info("🚀 Starting bot on Render...")
    
    # تعيين متغيرات البيئة
    os.environ.setdefault('RENDER', 'true')
    os.environ.setdefault('PYTHONUNBUFFERED', '1')
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs', 'temp', 'cache', 
        'security/logs', 'security/quarantine', 'security_logs'
    ]
    
    for dir_path in required_dirs:
        try:
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"✅ مجلد: {dir_path}")
        except Exception as e:
            logger.warning(f"⚠️ فشل في إنشاء مجلد {dir_path}: {e}")
    
    # تشغيل البوت
    logger.info("🤖 تشغيل البوت...")
    logger.info("🤖 Starting bot...")
    
    try:
        # استيراد وتشغيل start_render مباشرة
        import start_render
        import asyncio
        asyncio.run(start_render.main())
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
