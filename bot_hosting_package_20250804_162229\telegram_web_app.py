"""
نظام Telegram Web App لعرض تفاصيل المودات
يوفر صفحة ويب يمكن الوصول إليها من جميع الأجهزة عبر تيليجرام
"""

import logging
from flask import Flask, render_template_string, request
from flask_cors import CORS

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # السماح بالوصول من جميع النطاقات

# قالب HTML محسن لـ Telegram Web App
TELEGRAM_WEB_APP_TEMPLATE = """
<!DOCTYPE html>
<html lang="{{ lang }}" dir="{{ 'rtl' if lang == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{ mod_title }} - {{ 'تفاصيل المود' if lang == 'ar' else 'Mod Details' }}</title>
    
    <!-- Telegram Web App Script -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <style>
        :root {
            --tg-theme-bg-color: #ffffff;
            --tg-theme-text-color: #000000;
            --tg-theme-hint-color: #999999;
            --tg-theme-link-color: #2481cc;
            --tg-theme-button-color: #2481cc;
            --tg-theme-button-text-color: #ffffff;
        }

        body {
            background-color: var(--tg-theme-bg-color);
            color: var(--tg-theme-text-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .container {
            max-width: 100%;
            padding: 16px;
            margin: 0 auto;
        }

        .mod-card {
            background: var(--tg-theme-bg-color);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 16px;
        }

        .mod-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }

        .mod-title {
            font-size: 24px;
            font-weight: bold;
            margin: 16px 0;
            color: var(--tg-theme-text-color);
        }

        .mod-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px 0;
        }

        .info-item {
            background: rgba(0,0,0,0.05);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
        }

        .info-label {
            font-size: 12px;
            color: var(--tg-theme-hint-color);
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--tg-theme-text-color);
        }

        .mod-description {
            background: rgba(0,0,0,0.05);
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            line-height: 1.5;
        }

        .download-btn {
            background: var(--tg-theme-button-color);
            color: var(--tg-theme-button-text-color);
            border: none;
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 16px 0;
        }

        .download-btn:hover {
            opacity: 0.8;
            transform: translateY(-2px);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .thumbnail-container {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding: 8px 0;
            margin: 16px 0;
        }

        .thumbnail {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s;
        }

        .thumbnail.active {
            border-color: var(--tg-theme-button-color);
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: var(--tg-theme-hint-color);
        }

        .error {
            text-align: center;
            padding: 20px;
            color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
            border-radius: 8px;
            margin: 16px 0;
        }

        /* RTL Support */
        [dir="rtl"] .container {
            text-align: right;
        }

        [dir="rtl"] .mod-info {
            direction: rtl;
        }

        /* Dark theme support */
        @media (prefers-color-scheme: dark) {
            :root {
                --tg-theme-bg-color: #1a1a1a;
                --tg-theme-text-color: #ffffff;
                --tg-theme-hint-color: #666666;
            }
        }

        /* Mobile optimizations */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
            }
            
            .mod-title {
                font-size: 20px;
            }
            
            .download-btn {
                padding: 14px 20px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Loading State -->
        <div id="loading" class="loading">
            <div>{{ 'جاري التحميل...' if lang == 'ar' else 'Loading...' }}</div>
        </div>

        <!-- Error State -->
        <div id="error" class="error" style="display: none;">
            <div id="error-message"></div>
        </div>

        <!-- Main Content -->
        <div id="content" style="display: none;">
            <!-- Mod Card -->
            <div class="mod-card">
                <!-- Main Image -->
                <img id="main-image" class="mod-image" src="" alt="{{ 'صورة المود' if lang == 'ar' else 'Mod Image' }}">
                
                <!-- Thumbnails -->
                <div id="thumbnails" class="thumbnail-container" style="display: none;">
                    <!-- Thumbnails will be added here -->
                </div>

                <!-- Mod Title -->
                <h1 id="mod-title" class="mod-title"></h1>

                <!-- Mod Info Grid -->
                <div class="mod-info">
                    <div class="info-item">
                        <div class="info-label">{{ 'الإصدار' if lang == 'ar' else 'Version' }}</div>
                        <div id="mod-version" class="info-value"></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ 'التصنيف' if lang == 'ar' else 'Category' }}</div>
                        <div id="mod-category" class="info-value"></div>
                    </div>
                </div>

                <!-- Mod Description -->
                <div class="mod-description">
                    <div class="info-label">{{ 'الوصف' if lang == 'ar' else 'Description' }}</div>
                    <div id="mod-description"></div>
                </div>

                <!-- Download Button -->
                <button id="download-btn" class="download-btn" onclick="downloadMod()">
                    {{ 'تحميل المود' if lang == 'ar' else 'Download Mod' }}
                </button>
            </div>
        </div>
    </div>

    <script>
        // Telegram Web App initialization
        const tg = window.Telegram.WebApp;
        tg.ready();
        tg.expand();

        // Apply Telegram theme
        document.documentElement.style.setProperty('--tg-theme-bg-color', tg.themeParams.bg_color || '#ffffff');
        document.documentElement.style.setProperty('--tg-theme-text-color', tg.themeParams.text_color || '#000000');
        document.documentElement.style.setProperty('--tg-theme-hint-color', tg.themeParams.hint_color || '#999999');
        document.documentElement.style.setProperty('--tg-theme-link-color', tg.themeParams.link_color || '#2481cc');
        document.documentElement.style.setProperty('--tg-theme-button-color', tg.themeParams.button_color || '#2481cc');
        document.documentElement.style.setProperty('--tg-theme-button-text-color', tg.themeParams.button_text_color || '#ffffff');

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const modId = urlParams.get('id');
        const lang = urlParams.get('lang') || 'ar';
        const userId = urlParams.get('user_id');
        const channelId = urlParams.get('channel');

        let modData = null;
        let currentImageIndex = 0;

        // Load mod data
        async function loadModData() {
            if (!modId) {
                showError(lang === 'ar' ? 'معرف المود مفقود' : 'Mod ID is missing');
                return;
            }

            try {
                const response = await fetch(`/api/mod/${modId}?lang=${lang}&user_id=${userId}&channel=${channelId}`);
                const data = await response.json();

                if (data.success) {
                    modData = data.mod;
                    displayModData();
                } else {
                    showError(data.error || (lang === 'ar' ? 'فشل في تحميل بيانات المود' : 'Failed to load mod data'));
                }
            } catch (error) {
                console.error('Error loading mod data:', error);
                showError(lang === 'ar' ? 'خطأ في الاتصال' : 'Connection error');
            }
        }

        function displayModData() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';

            // Set mod title
            document.getElementById('mod-title').textContent = modData.title || (lang === 'ar' ? 'مود بدون عنوان' : 'Untitled Mod');

            // Set main image
            const mainImage = document.getElementById('main-image');
            const imageUrls = modData.image_urls || [];
            if (imageUrls.length > 0) {
                mainImage.src = imageUrls[0];
                setupImageGallery(imageUrls);
            } else {
                mainImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
            }

            // Set mod info
            document.getElementById('mod-version').textContent = modData.version || (lang === 'ar' ? 'غير محدد' : 'Not specified');
            document.getElementById('mod-category').textContent = getCategoryName(modData.category);

            // Set description
            const description = getDescription(modData);
            document.getElementById('mod-description').innerHTML = description;
        }

        function setupImageGallery(imageUrls) {
            if (imageUrls.length <= 1) return;

            const thumbnailsContainer = document.getElementById('thumbnails');
            thumbnailsContainer.style.display = 'flex';

            imageUrls.forEach((url, index) => {
                const thumbnail = document.createElement('img');
                thumbnail.src = url;
                thumbnail.className = 'thumbnail';
                if (index === 0) thumbnail.classList.add('active');
                
                thumbnail.onclick = () => {
                    currentImageIndex = index;
                    updateMainImage();
                    updateThumbnails();
                };

                thumbnailsContainer.appendChild(thumbnail);
            });
        }

        function updateMainImage() {
            const imageUrls = modData.image_urls || [];
            if (imageUrls.length > 0) {
                document.getElementById('main-image').src = imageUrls[currentImageIndex];
            }
        }

        function updateThumbnails() {
            const thumbnails = document.querySelectorAll('.thumbnail');
            thumbnails.forEach((thumb, index) => {
                thumb.classList.toggle('active', index === currentImageIndex);
            });
        }

        function getCategoryName(category) {
            const categories = {
                'ar': {
                    'addons': 'إضافات',
                    'shaders': 'شيدرات',
                    'texture_packs': 'حزم النسيج',
                    'seeds': 'بذور',
                    'maps': 'خرائط'
                },
                'en': {
                    'addons': 'Add-ons',
                    'shaders': 'Shaders',
                    'texture_packs': 'Texture Packs',
                    'seeds': 'Seeds',
                    'maps': 'Maps'
                }
            };

            return categories[lang]?.[category] || category || (lang === 'ar' ? 'غير محدد' : 'Not specified');
        }

        function getDescription(mod) {
            let description = '';
            
            if (lang === 'ar' && mod.telegram_description_ar) {
                description = mod.telegram_description_ar;
            } else if (lang === 'en' && mod.telegram_description_en) {
                description = mod.telegram_description_en;
            } else if (mod.description) {
                if (typeof mod.description === 'object') {
                    description = mod.description[lang] || mod.description.en || mod.description.ar || '';
                } else {
                    description = mod.description;
                }
            }

            return description || (lang === 'ar' ? 'لا يوجد وصف متاح' : 'No description available');
        }

        function downloadMod() {
            if (!modData || !modData.download_link) {
                tg.showAlert(lang === 'ar' ? 'رابط التحميل غير متوفر' : 'Download link not available');
                return;
            }

            // Open download link
            window.open(modData.download_link, '_blank');
            
            // Show success message
            tg.showAlert(lang === 'ar' ? 'تم بدء التحميل!' : 'Download started!');
            
            // Close the web app after a short delay
            setTimeout(() => {
                tg.close();
            }, 1500);
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }

        // Initialize the app
        loadModData();

        // Handle back button
        tg.onEvent('backButtonClicked', () => {
            tg.close();
        });

        // Show back button
        tg.BackButton.show();
    </script>
</body>
</html>
"""

@app.route('/telegram-mod-details')
def telegram_mod_details():
    """صفحة عرض تفاصيل المود المحسنة لـ Telegram Web App"""
    return render_template_string(TELEGRAM_WEB_APP_TEMPLATE, 
                                lang=request.args.get('lang', 'ar'))

# تم حذف API endpoint المكرر - يتم استخدام الـ endpoint من web_server.py

def get_local_ip():
    """الحصول على عنوان IP المحلي للشبكة"""
    try:
        # طريقة موثوقة للحصول على IP المحلي
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        try:
            # طريقة بديلة
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip if local_ip != "127.0.0.1" else "0.0.0.0"
        except Exception:
            return "0.0.0.0"

def run_telegram_web_app(port=5001, host=None):
    """تشغيل خادم Telegram Web App"""
    if host is None:
        # الكشف التلقائي عن IP المحلي
        local_ip = get_local_ip()
        host = "0.0.0.0"  # قبول الاتصالات من جميع الواجهات

        logger.info(f"Local IP detected: {local_ip}")
        logger.info(f"Server will be accessible at: http://{local_ip}:{port}")
        logger.info(f"And also at: http://localhost:{port}")

    logger.info(f"Starting Telegram Web App server on {host}:{port}...")
    app.run(host=host, port=port, debug=False, use_reloader=False)

if __name__ == '__main__':
    run_telegram_web_app()
