#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف بدء تشغيل البوت للاستضافة المجانية
Bot Startup File for Free Hosting
"""

import os
import sys
import asyncio
import logging
import requests
from datetime import datetime

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_telegram_conflicts():
    """إصلاح مشكلة Telegram getUpdates conflicts"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            print("⚠️ لم يتم العثور على token البوت")
            return False

        print("🔧 إصلاح مشكلة Telegram getUpdates conflicts...")

        # مسح webhook
        try:
            url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
            response = requests.post(url, json={"drop_pending_updates": True}, timeout=10)
            if response.status_code == 200 and response.json().get('ok'):
                print("✅ تم مسح webhook للبوت")
        except Exception as e:
            print(f"⚠️ تحذير في مسح webhook: {e}")

        # مسح التحديثات المعلقة
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
            params = {"offset": -1, "limit": 1, "timeout": 0}
            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    params['offset'] = last_update_id + 1
                    requests.get(url, params=params, timeout=10)
                    print("✅ تم مسح التحديثات المعلقة")
        except Exception as e:
            print(f"⚠️ تحذير في مسح التحديثات: {e}")

        return True

    except Exception as e:
        print(f"❌ خطأ في إصلاح Telegram: {e}")
        return False

def setup_environment():
    """إعداد البيئة للاستضافة مع إصلاح المشاكل"""

    # تحديد نوع الاستضافة
    hosting_type = "unknown"

    if os.environ.get('RAILWAY_ENVIRONMENT'):
        hosting_type = "railway"
        print("🚂 تشغيل على Railway")
    elif os.environ.get('RENDER'):
        hosting_type = "render"
        print("🎨 تشغيل على Render")
    elif os.environ.get('DYNO'):
        hosting_type = "heroku"
        print("🟣 تشغيل على Heroku")
    elif os.environ.get('KOYEB_APP_NAME'):
        hosting_type = "koyeb"
        print("🚀 تشغيل على Koyeb")
    else:
        hosting_type = "local"
        print("💻 تشغيل محلي")

    # إصلاح متغيرات البيئة المطلوبة
    required_env = {
        'BOT_TOKEN': '7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4',
        'ADMIN_CHAT_ID': '7513880877',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'LOG_LEVEL': 'INFO',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false'
    }

    for key, value in required_env.items():
        if not os.environ.get(key):
            os.environ[key] = value
            print(f"✅ تم تعيين {key}")

    # إعداد المنفذ
    port = int(os.environ.get('PORT', 10000))
    os.environ.setdefault('WEB_SERVER_PORT', str(port))

    return hosting_type

def check_requirements():
    """فحص المتطلبات الأساسية"""
    
    required_vars = [
        'BOT_TOKEN',
        'ADMIN_CHAT_ID',
        'SUPABASE_URL',
        'SUPABASE_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        print("\n🔧 تأكد من إضافة هذه المتغيرات في إعدادات الاستضافة:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    print("✅ جميع متغيرات البيئة موجودة")
    return True

def setup_logging():
    """إعداد نظام التسجيل"""
    
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    
    # إعداد التنسيق
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # إعداد معالج الكونسول
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # إعداد المسجل الرئيسي
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    root_logger.addHandler(console_handler)
    
    # تقليل مستوى تسجيل المكتبات الخارجية
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    print(f"📝 تم إعداد التسجيل بمستوى: {log_level}")

def create_health_server(hosting_type):
    """إنشاء خادم صحة للاستضافات التي تحتاجه"""
    
    if hosting_type in ['render', 'heroku']:
        try:
            from flask import Flask
            import threading
            
            app = Flask(__name__)
            
            @app.route('/')
            def home():
                return {
                    "status": "alive",
                    "bot": "Minecraft Mods Bot",
                    "timestamp": datetime.now().isoformat(),
                    "hosting": hosting_type
                }
            
            @app.route('/ping')
            def ping():
                return {"status": "pong"}
            
            @app.route('/health')
            def health():
                return {
                    "status": "healthy",
                    "uptime": "running",
                    "environment": os.environ.get('ENVIRONMENT', 'production')
                }
            
            def run_flask():
                port = int(os.environ.get('PORT', 5000))
                app.run(host='0.0.0.0', port=port, debug=False, use_reloader=False)
            
            # تشغيل Flask في thread منفصل
            flask_thread = threading.Thread(target=run_flask, daemon=True)
            flask_thread.start()
            
            print(f"🌐 خادم الصحة يعمل على المنفذ {os.environ.get('PORT', 5000)}")
            
        except ImportError:
            print("⚠️ Flask غير متوفر، تخطي خادم الصحة")

async def main():
    """الدالة الرئيسية لبدء تشغيل البوت"""

    print("🤖 بدء تشغيل بوت Minecraft Mods...")
    print(f"⏰ الوقت: {datetime.now()}")

    # إعداد البيئة
    hosting_type = setup_environment()

    # إصلاح مشاكل Telegram
    fix_telegram_conflicts()

    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        sys.exit(1)

    # إعداد التسجيل
    setup_logging()
    
    # إنشاء خادم الصحة إذا لزم الأمر
    create_health_server(hosting_type)
    
    # تشغيل البوت الرئيسي
    try:
        print("🚀 بدء تشغيل البوت...")
        
        # استيراد وتشغيل البوت الرئيسي
        from main import main as bot_main
        await bot_main()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        logging.exception("خطأ في تشغيل البوت")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
    except Exception as e:
        print(f"❌ خطأ فادح: {e}")
        sys.exit(1)
