import asyncio
import signal
import sys
import os
import re
import hashlib
import random
import sqlite3
import webbrowser
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import traceback

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد النشر
from deployment_config import setup_deployment
setup_deployment()

from modules.logger import logger
from modules.database import db
from modules.content_scraper import ContentScraper
from modules.youtube_analyzer import YouTubeAnalyzer
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.web_approval_system import web_approval_system
from modules.content_generator import ContentGenerator
from modules.publisher import PublisherManager
from modules.analytics import analytics
from modules.user_engagement import engagement_engine
from modules.visitor_analytics import visitor_analytics
from modules.intelligent_cms import intelligent_cms
from modules.ai_personality import ai_personality
from modules.article_performance_analyzer import article_performance_analyzer
from modules.content_optimizer import content_optimizer
from modules.advanced_seo_system import advanced_seo_system
from modules.api_integrations import api_manager, CoreWebVitalsAnalyzer, KeywordResearchAPI
from modules.microsoft_clarity import clarity_analyzer
from modules.ubersuggest_api import ubersuggest_api
from modules.advanced_monitoring import advanced_monitoring
from modules.performance_optimizer import performance_optimizer, DatabaseOptimizer
from modules.simple_backup import simple_backup
from modules.error_handler import (
    health_monitor, error_recovery, scheduler,
    async_retry_decorator
)

# إعداد النظام المحسن - يمكن تفعيله أو تعطيله
ENABLE_ENHANCED_SYSTEM = False  # غير هذا إلى True لتفعيل النظام المحسن (معطل مؤقتاً بسبب مشكلة الحلقة اللا نهائية)
ENHANCED_SYSTEMS_AVAILABLE = False

if ENABLE_ENHANCED_SYSTEM:
    logger.info("🔄 محاولة تفعيل النظام المحسن...")
else:
    logger.info("🔄 تم تعطيل النظام المحسن يدوياً - سيتم استخدام النظام التقليدي")

# متغيرات وهمية للتوافق
agent_state_manager = None
smart_db_manager = None
operation_manager = None
lifecycle_manager = None
AgentState = None
OperationType = None
OperationPriority = None
StartupMode = None
ShutdownReason = None
from config.settings import BotConfig, SourcesConfig, ContentConfig
from config.workflow_settings import WorkflowConfig, AdvancedWorkflowConfig
from config.gemini_keys_config import GeminiKeysConfig
from modules.advanced_api_manager import advanced_api_manager
from modules.error_fix_system import error_fix_system
from modules.improved_image_manager import improved_image_manager


# استيراد الأنظمة المحسنة الجديدة (Gemini بدلاً من RAG)
if ENABLE_ENHANCED_SYSTEM:
    try:
        from modules.gemini_agent_integration import gemini_agent_integration
        from modules.gemini_enhanced_system import gemini_enhanced_system
        from modules.multimodal_analyzer import multimodal_analyzer
        from modules.memory_system import memory_system

        # استيراد المدراء المطلوبين للنظام المحسن
        from modules.agent_state_manager import agent_state_manager, AgentState
        from modules.smart_database_manager import smart_db_manager
        from modules.operation_manager import operation_manager, OperationType, OperationPriority
        from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason

        ENHANCED_SYSTEMS_AVAILABLE = True
        logger.info("✅ تم تحميل الأنظمة المحسنة باستخدام Gemini 2.5 Pro بنجاح")
    except ImportError as e:
        ENHANCED_SYSTEMS_AVAILABLE = False
        logger.warning(f"⚠️ الأنظمة المحسنة غير متوفرة: {e}")
        logger.info("🔄 سيتم التحويل للنظام التقليدي")
else:
    logger.info("📝 تم تعطيل النظام المحسن يدوياً")

class GamingNewsBot:
    """الوكيل البرمجي الرئيسي لأخبار الألعاب"""
    
    def __init__(self):
        self.is_running = False
        self.scraper = ContentScraper()
        self.youtube_analyzer = None
        self.advanced_youtube_analyzer = None
        self.video_approval_system = None
        self.content_generator = None
        self.publisher = None
        self.processed_articles_count = 0
        self.startup_time = datetime.now()
        self.web_interface_opened = False
        self.web_server_thread = None
        self._components_initialized = False  # فلاج لمنع التهيئة المتكررة

        # الأنظمة المحسنة الجديدة
        self.api_manager = advanced_api_manager
        self.error_fixer = error_fix_system
        self.image_manager = improved_image_manager

        # بدء خادم الواجهة الويب
        self.start_web_server()

        # الأنظمة المحسنة (Gemini بدلاً من RAG)
        self.enhanced_systems_enabled = ENHANCED_SYSTEMS_AVAILABLE
        if self.enhanced_systems_enabled:
            self.enhanced_agent = gemini_agent_integration
            self.gemini_system = gemini_enhanced_system
            self.multimodal_analyzer = multimodal_analyzer
            self.memory_system = memory_system
            logger.info("🚀 تم تفعيل الأنظمة المحسنة باستخدام Gemini 2.5 Pro")
        else:
            logger.info("📝 تشغيل بالوضع الأساسي (بدون الأنظمة المحسنة)")

        # تسجيل دوال الاستدعاء مع مدير دورة الحياة (إذا كان متوفراً)
        if ENHANCED_SYSTEMS_AVAILABLE:
            self._register_lifecycle_callbacks()
            logger.info("✅ تم تسجيل دوال الاستدعاء مع النظام المحسن")
        else:
            # إعداد معالجات الإشارات التقليدية
            self.setup_signal_handlers()

    def _register_lifecycle_callbacks(self):
        """تسجيل دوال الاستدعاء مع مدير دورة الحياة"""
        if not lifecycle_manager:
            logger.warning("⚠️ مدير دورة الحياة غير متوفر")
            return

        # دوال البدء
        lifecycle_manager.register_startup_callback(self._on_startup)

        # دوال الإيقاف
        lifecycle_manager.register_shutdown_callback(self._on_shutdown)

        # دوال التنظيف
        lifecycle_manager.register_cleanup_callback(self._on_cleanup)

        logger.info("✅ تم تسجيل دوال الاستدعاء مع مدير دورة الحياة")

    async def _on_startup(self):
        """دالة استدعاء البدء"""
        logger.info("🚀 تنفيذ دالة استدعاء البدء...")

        # تهيئة المكونات
        await self._initialize_components()

        # اختبار الاتصالات
        await self._test_connections()

        # بدء المراقبة
        health_monitor.start_monitoring()
        advanced_monitoring.start_monitoring()

        logger.info("✅ اكتملت دالة استدعاء البدء")

    async def _on_shutdown(self):
        """دالة استدعاء الإيقاف"""
        logger.info("🛑 تنفيذ دالة استدعاء الإيقاف...")

        # إيقاف المراقبة
        health_monitor.stop_monitoring()
        advanced_monitoring.stop_monitoring()

        # حفظ الحالة
        self.save_current_state()

        logger.info("✅ اكتملت دالة استدعاء الإيقاف")

    async def _on_cleanup(self):
        """دالة استدعاء التنظيف"""
        logger.info("🧹 تنفيذ دالة استدعاء التنظيف...")

        # تنظيف الكاش
        performance_optimizer.clear_expired_cache()

        # إنشاء نسخة احتياطية
        simple_backup.create_full_backup()

        logger.info("✅ اكتملت دالة استدعاء التنظيف")

    def _start_enhanced_web_interface(self):
        """بدء الواجهة المحسنة"""
        try:
            if ENHANCED_SYSTEMS_AVAILABLE:
                from modules.enhanced_web_interface import enhanced_web_interface
                enhanced_web_interface.start_server(host='localhost', port=5000, debug=False)
                logger.info("✅ تم بدء الواجهة المحسنة")
            else:
                # العودة للواجهة التقليدية
                self.open_web_interface()
        except Exception as e:
            logger.error(f"❌ فشل في بدء الواجهة المحسنة: {e}")
            # العودة للواجهة التقليدية
            self.open_web_interface()

    def open_web_interface(self):
        """فتح واجهة الويب تلقائياً"""
        if not self.web_interface_opened:
            def open_browser():
                time.sleep(3)  # انتظار 3 ثوان لضمان تشغيل الخادم
                url = "http://localhost:5000"
                try:
                    webbrowser.open(url)
                    logger.info(f"🌐 تم فتح واجهة الويب تلقائياً: {url}")
                    print(f"🌐 تم فتح واجهة الويب تلقائياً: {url}")
                    self.web_interface_opened = True
                except Exception as e:
                    logger.warning(f"⚠️ لم يتمكن من فتح المتصفح تلقائياً: {e}")
                    print(f"⚠️ لم يتمكن من فتح المتصفح تلقائياً: {e}")
                    print(f"📋 يرجى فتح المتصفح يدوياً والانتقال إلى: {url}")

            # بدء thread لفتح المتصفح
            browser_thread = threading.Thread(target=open_browser, daemon=True)
            browser_thread.start()

    def start_web_server(self):
        """بدء خادم الواجهة الويب"""
        try:
            def run_web_server():
                try:
                    # استيراد خادم الواجهة
                    from web_api import run_server

                    # تحديد المنفذ (مهم للاستضافة)
                    port = int(os.getenv('PORT', 5000))
                    host = '0.0.0.0' if os.getenv('RENDER') or os.getenv('HEROKU') else 'localhost'

                    logger.info(f"🌐 بدء خادم الواجهة الويب على {host}:{port}")
                    run_server(host=host, port=port, debug=False)

                except Exception as e:
                    logger.error(f"❌ خطأ في تشغيل خادم الواجهة: {e}")

            # تشغيل الخادم في thread منفصل
            self.web_server_thread = threading.Thread(target=run_web_server, daemon=True)
            self.web_server_thread.start()

            # انتظار قصير لبدء الخادم
            time.sleep(2)

            # فتح المتصفح إذا كان محلياً
            if not (os.getenv('RENDER') or os.getenv('HEROKU')):
                self.open_web_interface()

        except Exception as e:
            logger.error(f"❌ فشل في بدء خادم الواجهة الويب: {e}")

    def setup_signal_handlers(self):
        """إعداد معالجات إشارات النظام"""
        try:
            def signal_handler(signum, _):
                logger.info(f"📡 تم استلام إشارة {signum}، بدء الإيقاف الآمن...")
                asyncio.create_task(self.graceful_shutdown())

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        except ValueError as e:
            # هذا يحدث عندما لا نكون في الـ main thread
            logger.warning(f"⚠️ لا يمكن إعداد معالجات الإشارات: {e}")
            logger.info("ℹ️ سيتم الاعتماد على آليات الإيقاف الأخرى")
    
    def load_saved_state(self):
        """تحميل الحالة المحفوظة للبوت"""
        saved_state = error_recovery.load_state()
        
        if saved_state:
            self.processed_articles_count = saved_state.get('processed_articles_count', 0)
            logger.info(f"📂 تم تحميل الحالة المحفوظة: {self.processed_articles_count} مقال معالج")
    
    def save_current_state(self):
        """حفظ الحالة الحالية للبوت"""
        state_data = {
            'processed_articles_count': self.processed_articles_count,
            'startup_time': self.startup_time.isoformat(),
            'last_save_time': datetime.now().isoformat()
        }
        
        error_recovery.save_state(state_data)
    
    async def initialize(self):
        """تهيئة جميع مكونات البوت مع دعم النظام المحسن"""
        try:
            logger.start_session_log()

            if ENHANCED_SYSTEMS_AVAILABLE and lifecycle_manager:
                logger.info("🚀 بدء تهيئة وكيل أخبار الألعاب باستخدام النظام الذكي...")

                # التحقق من التكوين
                if not BotConfig.validate_config():
                    raise Exception("❌ التكوين غير مكتمل، يرجى التحقق من متغيرات البيئة")

                # بدء النظام الذكي لإدارة دورة الحياة
                success = await lifecycle_manager.smart_startup(StartupMode.NORMAL)

                if not success:
                    logger.error("❌ فشل في بدء النظام الذكي، التحويل للنظام التقليدي")
                    return await self._initialize_traditional()

                logger.info("✅ تم تهيئة البوت بنجاح باستخدام النظام الذكي!")
                return True
            else:
                logger.info("🚀 بدء تهيئة وكيل أخبار الألعاب باستخدام النظام التقليدي...")
                return await self._initialize_traditional()

        except Exception as e:
            logger.critical("❌ فشل في تهيئة البوت", e)

            if ENHANCED_SYSTEMS_AVAILABLE and lifecycle_manager:
                # محاولة الإيقاف الطارئ
                try:
                    await lifecycle_manager.emergency_shutdown(f"فشل التهيئة: {str(e)}")
                except:
                    pass

            return False

    async def _initialize_traditional(self):
        """التهيئة التقليدية (النظام الأصلي)"""
        try:
            # التحقق من التكوين
            if not BotConfig.validate_config():
                raise Exception("❌ التكوين غير مكتمل، يرجى التحقق من متغيرات البيئة")

            # تهيئة المولدات والناشرين
            await self._initialize_components()

            # اختبار الاتصالات
            await self._test_connections()

            # بدء مراقبة النظام
            health_monitor.start_monitoring()

            # بدء نظام المراقبة المتقدم
            advanced_monitoring.start_monitoring()
            logger.info("🔍 تم بدء نظام المراقبة المتقدم")

            # تحسين قاعدة البيانات
            DatabaseOptimizer.add_missing_indexes()
            DatabaseOptimizer.optimize_database()

            # إنشاء نسخة احتياطية أولية
            simple_backup.create_full_backup()

            logger.info("✅ تم تهيئة البوت بنجاح بالنظام التقليدي!")
            return True

        except Exception as e:
            logger.critical("❌ فشل في التهيئة التقليدية", e)
            return False
    
    async def _initialize_components(self):
        """تهيئة المكونات الفردية"""

        # تحقق من عدم التهيئة المتكررة
        if self._components_initialized:
            logger.info("⚠️ المكونات مهيأة بالفعل، تخطي التهيئة المتكررة")
            return

        # تهيئة محلل يوتيوب التقليدي
        self.youtube_analyzer = YouTubeAnalyzer()
        logger.info("✅ تم تهيئة محلل يوتيوب التقليدي")

        # تهيئة محلل يوتيوب المتقدم (الأولوية الأولى) مع النظام المحسن
        self.advanced_youtube_analyzer = AdvancedYouTubeAnalyzer()
        logger.info("✅ تم تهيئة محلل يوتيوب المتقدم مع نظام تحويل النص إلى صوت المحسن")

        # تهيئة نظام تحويل النص إلى صوت المحسن
        try:
            from modules.enhanced_speech_integration import enhanced_speech_integration
            self.enhanced_speech = enhanced_speech_integration
            logger.info("🎤 تم تهيئة نظام تحويل النص إلى صوت المحسن بنجاح")
            logger.info("🎯 الخدمات المتاحة: AssemblyAI, Speechmatics, IBM Watson, Azure, Google Cloud, Wit.ai + Whisper")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة النظام المحسن: {e}")
            logger.info("🔄 سيتم استخدام Whisper التقليدي كبديل")
            self.enhanced_speech = None

        # تهيئة نظام الموافقة على الفيديوهات (الويب)
        self.video_approval_system = web_approval_system
        logger.info("✅ تم تهيئة نظام الموافقة على الفيديوهات (الويب)")

        # تهيئة مولد المحتوى
        self.content_generator = ContentGenerator()
        logger.info("✅ تم تهيئة مولد المحتوى")

        # تهيئة الناشر (Blogger فقط - تم إزالة Telegram)
        blogger_config = {
            'client_id': BotConfig.BLOGGER_CLIENT_ID,
            'client_secret': BotConfig.BLOGGER_CLIENT_SECRET,
            'blog_id': BotConfig.BLOGGER_BLOG_ID
        }

        # إزالة Telegram - النشر على Blogger فقط
        self.publisher = PublisherManager(blogger_config, None)
        logger.info("✅ تم تهيئة مدير النشر (Blogger فقط - تم إزالة Telegram)")

        # تعيين فلاج التهيئة
        self._components_initialized = True
    
    async def _test_connections(self):
        """اختبار جميع الاتصالات"""
        logger.info("🔍 اختبار الاتصالات...")
        
        # اختبار قاعدة البيانات
        stats = db.get_stats_summary(1)
        if not stats:
            raise Exception("فشل في الاتصال بقاعدة البيانات")
        
        # اختبار الناشر
        connection_results = await self.publisher.test_all_connections()
        
        if not connection_results['overall']:
            logger.warning("⚠️ بعض الاتصالات لا تعمل، سيتم المتابعة مع المكونات المتاحة")
        
        logger.info("✅ اكتمل اختبار الاتصالات")
    
    async def run(self):
        """تشغيل البوت الرئيسي مع دعم النظام المحسن"""
        if not await self.initialize():
            logger.critical("❌ فشل في التهيئة، إيقاف البوت")
            return

        self.is_running = True

        if ENHANCED_SYSTEMS_AVAILABLE:
            logger.info("🤖 بدء تشغيل البوت باستخدام النظام الذكي، يعمل 24/7...")
            await self._run_enhanced()
        else:
            logger.info("🤖 بدء تشغيل البوت باستخدام النظام التقليدي، يعمل 24/7...")
            await self._run_traditional()

    async def _run_enhanced(self):
        """تشغيل النظام المحسن"""
        try:
            # التحقق من توفر مدير العمليات وحالته
            if not operation_manager:
                logger.error("❌ مدير العمليات غير متوفر، التحويل للنظام التقليدي")
                await self._run_traditional()
                return

            # التحقق من أن مدير العمليات تم بدء تشغيله
            if not hasattr(operation_manager, '_worker_task') or not operation_manager._worker_task:
                logger.warning("⚠️ مدير العمليات لم يتم بدء تشغيله، محاولة بدء التشغيل...")
                try:
                    await operation_manager.start()
                    logger.info("✅ تم بدء مدير العمليات بنجاح")
                except Exception as e:
                    logger.error(f"❌ فشل في بدء مدير العمليات: {e}")
                    logger.info("🔄 التحويل للنظام التقليدي...")
                    await self._run_traditional()
                    return

            # بدء الواجهة المحسنة
            print("🌐 بدء الواجهة المحسنة للتحكم في الوكيل...")
            print("📋 الواجهة متاحة على: http://localhost:5000")
            self._start_enhanced_web_interface()

            while self.is_running:
                try:
                    # تشغيل دورة العمل كعملية في مدير العمليات
                    logger.info("🔄 إرسال دورة العمل إلى مدير العمليات...")
                    operation_id = operation_manager.submit_operation(
                        operation_type=OperationType.CONTENT_COLLECTION,
                        func=self._main_cycle,
                        priority=OperationPriority.HIGH,
                        timeout_seconds=3600,  # ساعة واحدة كحد أقصى
                        metadata={'cycle_type': 'main_content_cycle'}
                    )

                    if not operation_id:
                        logger.error("❌ فشل في إرسال العملية إلى مدير العمليات")
                        # العودة للنظام التقليدي
                        await self._run_traditional()
                        return

                    logger.info(f"✅ تم إرسال العملية بنجاح: {operation_id}")

                    # انتظار اكتمال العملية مع timeout
                    operation_timeout = 3700  # أكثر قليلاً من timeout العملية
                    start_time = time.time()

                    while True:
                        # فحص timeout
                        if time.time() - start_time > operation_timeout:
                            logger.warning("⚠️ انتهت مهلة انتظار العملية، إلغاء العملية...")
                            # محاولة إلغاء العملية
                            try:
                                operation_manager.cancel_operation(operation_id)
                            except:
                                pass
                            break

                        operation_status = operation_manager.get_operation_status(operation_id)
                        if not operation_status:
                            logger.warning("⚠️ فقدت معلومات العملية")
                            break

                        if operation_status['state'] in ['completed', 'failed', 'cancelled']:
                            logger.info(f"✅ اكتملت العملية بحالة: {operation_status['state']}")
                            break

                        await asyncio.sleep(5)  # فحص كل 5 ثوان

                except Exception as e:
                    logger.error(f"❌ خطأ في حلقة العمليات المحسنة: {e}")
                    # في حالة الخطأ، انتظار قبل المحاولة مرة أخرى
                    await asyncio.sleep(30)
                    continue

                # الحصول على نتائج العملية
                if operation_status and operation_status['state'] == 'completed':
                    # يمكن الحصول على النتائج من العملية هنا
                    published_count = 0  # سيتم تحديثه لاحقاً
                    generated_articles = []
                else:
                    published_count = 0
                    generated_articles = []
                    logger.warning(f"⚠️ فشلت دورة العمل: {operation_status.get('error', 'غير محدد') if operation_status else 'عملية غير موجودة'}")

                # حساب الوقت الأمثل للنشر التالي
                await self._calculate_next_cycle_time(published_count, generated_articles)

        except Exception as e:
            logger.critical("❌ خطأ حرج في التشغيل المحسن", e)

            # تسجيل تفاصيل إضافية عن الخطأ
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'operation_manager_available': operation_manager is not None,
                'lifecycle_manager_available': lifecycle_manager is not None,
                'enhanced_systems_available': ENHANCED_SYSTEMS_AVAILABLE
            }
            logger.critical(f"🚨 تفاصيل الخطأ: {error_details}")

            await self._handle_critical_error(e)

        finally:
            await self.graceful_shutdown()

    async def _run_traditional(self):
        """تشغيل النظام التقليدي"""
        try:
            # فتح واجهة الويب تلقائياً
            print("🌐 فتح واجهة الويب للتحكم في الوكيل...")
            print("📋 الواجهة متاحة على: http://localhost:5000")
            self.open_web_interface()

            while self.is_running:
                cycle_result = await self._main_cycle()
                published_count = cycle_result.get('published_count', 0) if cycle_result else 0
                generated_articles = cycle_result.get('generated_articles', []) if cycle_result else []

                # حساب الوقت الأمثل للنشر التالي
                await self._calculate_next_cycle_time(published_count, generated_articles)

        except Exception as e:
            logger.critical("❌ خطأ حرج في التشغيل التقليدي", e)
            await self._handle_critical_error(e)

        finally:
            await self.graceful_shutdown()

    async def _calculate_next_cycle_time(self, published_count, generated_articles):
        """حساب وقت الدورة التالية مع تشغيل المهام الخلفية"""
        try:
            logger.info("🧠 أليكس يحسب الوقت الأمثل للنشر التالي...")

            # تمرير بيانات المقالات المنشورة للتحليل
            article_data = None
            if published_count > 0 and generated_articles:
                article_data = generated_articles[0]  # استخدام أول مقال كمرجع

            optimal_timing = await intelligent_cms.calculate_optimal_publishing_time(article_data)

            # استخدام إعدادات تدفق العمل مع التحسين الذكي
            base_wait_time = optimal_timing.get('wait_seconds', WorkflowConfig.DEFAULT_CYCLE_WAIT_TIME)

            # تطبيق حدود الوقت من الإعدادات
            wait_time = max(WorkflowConfig.MIN_CYCLE_WAIT_TIME,
                           min(WorkflowConfig.MAX_CYCLE_WAIT_TIME, base_wait_time))

            wait_hours = wait_time / 3600
            confidence = optimal_timing.get('confidence_score', 50.0)
            reasoning = optimal_timing.get('reasoning', ['تحليل ذكي'])

            # تسجيل القرار الذكي
            logger.info(f"⏰ أليكس قرر: انتظار {wait_hours:.1f} ساعة للنشر التالي")
            logger.info(f"🎯 مستوى الثقة: {confidence:.1f}%")
            logger.info(f"💡 الأسباب: {', '.join(reasoning)}")

            if published_count > 0:
                logger.info(f"✅ تم نشر {published_count} مقال - الوقت التالي محسوب ذكياً")
                logger.info("🔧 بدء المهام الخلفية أثناء فترة الانتظار...")

                # تشغيل المهام الخلفية أثناء الانتظار
                await self._run_background_tasks_during_wait(wait_time)
            else:
                logger.info(f"⚠️ لم يتم نشر مقالات - سيعيد أليكس المحاولة خلال {wait_hours:.1f} ساعة")
                # انتظار عادي بدون مهام خلفية مكثفة
                await asyncio.sleep(wait_time)

        except Exception as e:
            logger.error(f"❌ خطأ في حساب وقت الدورة التالية: {e}")
            # انتظار افتراضي
            await asyncio.sleep(2 * 3600)  # ساعتين
    
    async def _main_cycle(self):
        """دورة العمل الرئيسية المحسنة - النشر أولاً ثم التحليل"""
        cycle_start_time = datetime.now()
        logger.info("🚀 بدء دورة عمل ذكية جديدة مع أليكس...")

        try:
            # حفظ الحالة قبل البدء
            self.save_current_state()

            # المرحلة 1: جمع ومعالجة ونشر المحتوى الجديد (الأولوية القصوى)
            logger.info("📰 المرحلة 1: جمع ومعالجة ونشر المحتوى الجديد...")

            # 1. جمع المحتوى الجديد
            collected_content = await self._collect_new_content_fast()

            # 2. معالجة وتوليد المقالات
            if collected_content:
                generated_articles = await self._process_content_with_engagement(collected_content)

                # 3. نشر المقالات فوراً
                published_count = await self._publish_articles_with_analytics(generated_articles)

                if published_count > 0:
                    logger.info(f"✅ تم نشر {published_count} مقال جديد بنجاح!")

                    # تحديث الإحصائيات الأساسية
                    self._update_statistics(len(collected_content), published_count)

                    # رد شخصي من أليكس
                    success_response = ai_personality.generate_personality_response(
                        f"نجاح نشر {published_count} مقالات جديدة",
                        'enthusiastic'
                    )
                    logger.info(f"🤖 أليكس: {success_response}")

                    scheduler.mark_successful_run()

                    # إرجاع النتائج فوراً للانتقال لفترة الانتظار
                    return {
                        'published_count': published_count,
                        'collected_count': len(collected_content),
                        'generated_articles': generated_articles,
                        'success': True,
                        'cycle_duration': (datetime.now() - cycle_start_time).total_seconds()
                    }

            # إذا لم يتم نشر أي محتوى، ابحث عن محتوى بديل
            logger.info("🔍 لم يتم العثور على محتوى جديد، البحث عن بدائل...")
            alternative_content = await self._find_alternative_content()

            if alternative_content:
                generated_articles = await self._process_content_with_engagement(alternative_content)
                published_count = await self._publish_articles_with_analytics(generated_articles)

                if published_count > 0:
                    logger.info(f"✅ تم نشر {published_count} مقال بديل بنجاح!")
                    self._update_statistics(len(alternative_content), published_count)
                    scheduler.mark_successful_run()

                    return {
                        'published_count': published_count,
                        'collected_count': len(alternative_content),
                        'generated_articles': generated_articles,
                        'success': True,
                        'cycle_duration': (datetime.now() - cycle_start_time).total_seconds()
                    }

            # إذا لم يتم نشر أي شيء
            logger.warning("⚠️ لم يتم نشر أي محتوى في هذه الدورة")
            alex_response = ai_personality.generate_personality_response(
                "لم أجد محتوى مناسب للنشر، سأعيد المحاولة لاحقاً",
                'patient'
            )
            logger.info(f"🤖 أليكس: {alex_response}")
            scheduler.mark_successful_run()

            return {
                'published_count': 0,
                'collected_count': 0,
                'generated_articles': [],
                'success': True,
                'cycle_duration': (datetime.now() - cycle_start_time).total_seconds()
            }

        except Exception as e:
            logger.error("❌ خطأ في دورة العمل الرئيسية", e)
            scheduler.mark_failed_run(e)

            # محاولة الاستعادة
            await self._attempt_error_recovery(e)

            # إرجاع نتائج فاشلة
            return {
                'published_count': 0,
                'collected_count': 0,
                'generated_articles': [],
                'success': False,
                'cycle_duration': (datetime.now() - cycle_start_time).total_seconds()
            }
    
    @async_retry_decorator(max_retries=2, base_delay=30)
    async def _collect_content(self) -> List[Dict]:
        """جمع المحتوى من جميع المصادر مع منطق الأولوية"""
        all_content = []
        
        try:
            # 1. البحث العميق المتقدم باستخدام Tavily (الأولوية الأولى) + APIs متعددة
            logger.info("� بدء البحث العميق المتقدم باستخدام Tavily و APIs متعددة...")

            # كلمات مفتاحية محسنة للبحث العميق
            advanced_keywords = [
                'gaming news today',
                'video game updates',
                'new game releases',
                'gaming industry news',
                'latest gaming announcements'
            ]

            advanced_content = []
            for keyword in advanced_keywords[:3]:  # أفضل 3 كلمات مفتاحية
                try:
                    # استخدام النظام المحسن الجديد مع النماذج الاحتياطية للذكاء الاصطناعي
                    logger.info(f"🚀 استخدام النظام المحسن مع النماذج الاحتياطية للبحث عن: {keyword}")

                    # المحاولة الأولى: النظام المحسن مع AI
                    enhanced_results = await self.scraper.enhanced_search_with_ai_fallbacks(keyword, 10, "balanced")
                    if enhanced_results:
                        advanced_content.extend(enhanced_results)
                        logger.info(f"✅ النظام المحسن مع AI: {len(enhanced_results)} نتيجة عالية الجودة لـ {keyword}")

                        # عرض معلومات إضافية عن النتائج
                        ai_enhanced_count = sum(1 for r in enhanced_results if r.get('ai_enhanced', False))
                        if ai_enhanced_count > 0:
                            logger.info(f"🤖 {ai_enhanced_count} نتيجة معززة بالذكاء الاصطناعي")
                    else:
                        # احتياطي: النظام المتقدم التقليدي
                        logger.info("🔄 النظام المحسن فشل، محاولة النظام المتقدم التقليدي...")

                        fallback_results = await self.scraper.advanced_search_with_fallbacks(keyword, 10)
                        if fallback_results:
                            advanced_content.extend(fallback_results)
                            logger.info(f"✅ النظام المتقدم التقليدي: {len(fallback_results)} نتيجة لـ {keyword}")
                        else:
                            # احتياطي أخير: النظام القديم
                            logger.info("🔄 النظام المتقدم فشل، محاولة النظام القديم...")

                            # أولاً: محاولة Tavily للبحث العميق (مع Gemini 2.5 Pro كبديل تلقائي)
                            tavily_results = await self.scraper.advanced_search_and_extract_with_tavily(keyword, 8)
                            if tavily_results:
                                advanced_content.extend(tavily_results)
                                logger.info(f"🔍 Tavily/Gemini: {len(tavily_results)} نتيجة لـ {keyword}")
                            else:
                                # احتياطي: SerpAPI
                                logger.info("🔄 Tavily وGemini فشلا، محاولة استخدام SerpAPI...")
                                serpapi_results = await self.scraper.advanced_search_and_extract_with_serpapi(keyword, 10)
                                if serpapi_results:
                                    advanced_content.extend(serpapi_results)
                                    logger.info(f"🚀 SerpAPI (احتياطي): {len(serpapi_results)} نتيجة لـ {keyword}")
                                else:
                                    # احتياطي أخير: بحث Google التقليدي
                                    logger.info("🔄 SerpAPI فشل، محاولة استخدام Google Search...")
                                    google_results = self.scraper.extract_articles(keyword, "google_search")
                                    if google_results:
                                        advanced_content.extend(google_results)
                                        logger.info(f"🔍 Google Search (احتياطي): {len(google_results)} نتيجة لـ {keyword}")
                                    else:
                                        logger.warning(f"⚠️ فشلت جميع محركات البحث عن: {keyword}")

                    await asyncio.sleep(2)  # تأخير مناسب
                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث المتقدم عن {keyword}: {e}")
                    continue

            if advanced_content:
                logger.info(f"🚀 البحث المتقدم: تم العثور على {len(advanced_content)} مقال عالي الجودة")
                all_content.extend(advanced_content)

            # إذا لم يتم العثور على محتوى من البحث المتقدم، استخدم الطرق التقليدية
            if not all_content:
                logger.info("🤔 لم يتم العثور على محتوى من البحث المتقدم، التحول إلى الطرق التقليدية...")
                # 2. جمع من المواقع التقليدية كخطة بديلة
                website_content = await self._collect_from_websites()
                all_content.extend(website_content)
            else:
                website_content = [] # لضمان عدم تكرار المحتوى

            # 3. التحقق مما إذا كانت هناك مقالات حديثة
            thirty_days_ago = datetime.now() - timedelta(days=30)
            recent_articles = [
                article for article in website_content
                if article.get('published_date') and article['published_date'] > thirty_days_ago
            ]

            if recent_articles:
                logger.info(f"📰 تم العثور على {len(recent_articles)} مقال حديث من مواقع الويب التقليدية")
                all_content.extend(recent_articles)
            else:
                logger.info("🤔 لم يتم العثور على مقالات حديثة في المواقع، سيتم البحث في يوتيوب")
                # 4. إذا لم تكن هناك مقالات حديثة، ابحث في يوتيوب
                youtube_content = await self._collect_from_youtube()
                all_content.extend(youtube_content)
                # أضف المحتوى القديم من المواقع أيضًا، ربما يكون مفيدًا
                all_content.extend(website_content)

            # 4. تصفية المحتوى المكرر
            unique_content = await self._filter_duplicate_content(all_content)
            
            logger.info(f"📰 تم جمع {len(unique_content)} عنصر محتوى فريد")
            
            return unique_content
            
        except Exception as e:
            logger.error("❌ فشل في جمع المحتوى", e)
            db.log_error("content_collection_error", str(e))
            raise
    
    async def _collect_from_websites(self) -> List[Dict]:
        """جمع المحتوى من المواقع الثابتة و RSS feeds"""
        website_content = []

        # 1. جمع من RSS feeds للحصول على أحدث الأخبار
        try:
            logger.info("📡 جمع المحتوى من RSS feeds...")
            rss_articles = self.scraper.extract_from_rss_feeds()
            if rss_articles:
                website_content.extend(rss_articles)
                logger.info(f"✅ تم جمع {len(rss_articles)} مقال من RSS feeds")
            else:
                logger.info("📭 لم يتم العثور على مقالات في RSS feeds")
        except Exception as e:
            logger.error("❌ فشل في جمع المحتوى من RSS feeds", e)

        # 2. جمع من المصادر الثابتة
        logger.info("🌐 جمع المحتوى من المصادر الثابتة...")

        # إعادة تعيين المصادر المعطلة كل 24 ساعة
        if hasattr(self, '_last_reset_time'):
            if (datetime.now() - self._last_reset_time).total_seconds() > 86400:  # 24 ساعة
                self.scraper.reset_failed_sources()
                self._last_reset_time = datetime.now()
        else:
            self._last_reset_time = datetime.now()

        # المواقع الرسمية مع تحسينات
        logger.info("🌐 جمع المحتوى من المواقع الرسمية...")
        for url in SourcesConfig.OFFICIAL_SOURCES[:5]:  # أفضل 5 مواقع
            try:
                articles = self.scraper.extract_articles(url, "official_site")
                if articles:
                    # فلترة المقالات الحديثة فقط
                    recent_articles = [
                        article for article in articles
                        if self._is_article_recent(article)
                    ]
                    website_content.extend(recent_articles)
                    logger.info(f"✅ تم جمع {len(recent_articles)} مقال حديث من {url}")
                else:
                    logger.info(f"📭 لم يتم العثور على مقالات في {url}")
                await asyncio.sleep(2)  # تأخير أطول للاحترام
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)
        
        # مواقع الألعاب
        for url in SourcesConfig.GAMING_SITES:
            try:
                articles = self.scraper.extract_articles(url, "gaming_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)
        
        # المواقع العربية
        for url in SourcesConfig.ARABIC_SITES:
            try:
                articles = self.scraper.extract_articles(url, "arabic_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)

        # مواقع المراجعات
        for url in SourcesConfig.REVIEW_SITES:
            try:
                articles = self.scraper.extract_articles(url, "review_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)

        # المنتديات
        for url in SourcesConfig.FORUM_SITES:
            try:
                articles = self.scraper.extract_articles(url, "forum_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)
        
        logger.info(f"📊 إجمالي المحتوى المجمع: {len(website_content)} مقال")
        return website_content

    def _is_article_recent(self, article: Dict) -> bool:
        """فحص ما إذا كان المقال حديث (آخر 7 أيام)"""
        try:
            published_date = article.get('published_date')
            if not published_date:
                return True  # إذا لم يكن هناك تاريخ، اعتبره حديث

            if isinstance(published_date, str):
                from dateutil import parser
                published_date = parser.parse(published_date)

            days_old = (datetime.now() - published_date).days

            # مقال حديث إذا كان أقل من 7 أيام أو يحتوي على كلمات دالة على الحداثة
            if days_old <= 7:
                return True

            # فحص إضافي للكلمات الدالة على الحداثة في العنوان أو الملخص
            text_to_check = f"{article.get('title', '')} {article.get('summary', '')}".lower()
            recent_indicators = [
                'today', 'yesterday', 'this week', 'recently', 'just announced',
                'breaking', 'latest', 'new', 'updated', 'fresh', 'current',
                '2025', 'january 2025', 'this month'
            ]

            return any(indicator in text_to_check for indicator in recent_indicators)

        except Exception as e:
            logger.debug(f"خطأ في فحص حداثة المقال: {e}")
            return True  # في حالة الخطأ، اعتبره حديث
    
    async def _collect_from_youtube(self) -> List[Dict]:
        """جمع المحتوى من يوتيوب"""
        try:
            videos = self.youtube_analyzer.search_videos(max_results=10)
            
            youtube_content = []
            for video in videos:
                article = self.youtube_analyzer.generate_article_from_video(video, BotConfig.TXTIFY_API_URL)
                if article:
                    youtube_content.append(article)
            
            return youtube_content
            
        except Exception as e:
            logger.warning("⚠️ فشل في جمع المحتوى من يوتيوب", e)
            return []
    
    async def _filter_duplicate_content(self, content_list: List[Dict]) -> List[Dict]:
        """تصفية المحتوى المكرر بخوارزمية محسنة"""
        unique_content = []
        seen_titles = set()
        seen_content_hashes = set()

        for content in content_list:
            try:
                title = content.get('title', '').lower().strip()
                content_text = content.get('content', '')

                # تنظيف العنوان للمقارنة
                clean_title = re.sub(r'[^\w\s]', '', title)
                clean_title = re.sub(r'\s+', ' ', clean_title).strip()

                # فحص التكرار المحلي أولاً (في نفس الدورة)
                if clean_title in seen_titles:
                    logger.debug(f"⏭️ تخطي محتوى مكرر محلياً: {title}")
                    continue

                # فحص تجزئة المحتوى
                content_hash = hashlib.md5(content_text.encode('utf-8')).hexdigest()
                if content_hash in seen_content_hashes:
                    logger.debug(f"⏭️ تخطي محتوى مكرر (نفس المحتوى): {title}")
                    continue

                # فحص التكرار في قاعدة البيانات
                is_duplicate, reason = db.is_duplicate_content(
                    content_text,
                    content.get('title', ''),
                    content.get('keywords', [])
                )

                if not is_duplicate:
                    # فحص جودة المحتوى
                    if self._is_quality_content(content):
                        unique_content.append(content)
                        seen_titles.add(clean_title)
                        seen_content_hashes.add(content_hash)
                    else:
                        logger.debug(f"⏭️ تخطي محتوى منخفض الجودة: {title}")
                else:
                    logger.debug(f"⏭️ تخطي محتوى مكرر في قاعدة البيانات: {title} - {reason}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في فحص التكرار للمحتوى: {content.get('title', 'غير محدد')}", e)
                # في حالة الخطأ، أضف المحتوى للأمان إذا كان يبدو جيداً
                if self._is_quality_content(content):
                    unique_content.append(content)

        return unique_content

    def _enhance_source_content_for_retry(self, content: Dict, quality_review: Dict) -> Dict:
        """تحسين المحتوى المصدر للمحاولة الثانية بناءً على مشاكل الجودة"""
        enhanced_content = content.copy()

        try:
            missing_concepts = []
            for issue in quality_review.get('issues', []):
                if 'مفقود:' in issue or 'Missing:' in issue:
                    # استخراج المفاهيم المفقودة
                    missing_part = issue.split('مفقود:')[-1] if 'مفقود:' in issue else issue.split('Missing:')[-1]
                    missing_concepts.extend([concept.strip() for concept in missing_part.split(',')])

            if missing_concepts:
                # إضافة المفاهيم المفقودة للكلمات المفتاحية
                current_keywords = enhanced_content.get('keywords', [])
                current_keywords.extend(missing_concepts)
                enhanced_content['keywords'] = list(set(current_keywords))

                # تحسين الملخص لتضمين المفاهيم المفقودة
                current_summary = enhanced_content.get('summary', enhanced_content.get('content', ''))

                # إضافة جملة تشير للمفاهيم المفقودة
                missing_concepts_text = ', '.join(missing_concepts)
                enhanced_summary = f"{current_summary}\n\nهذا المحتوى يتضمن معلومات حول: {missing_concepts_text}"

                enhanced_content['summary'] = enhanced_summary
                enhanced_content['retry_enhancement'] = True

                logger.info(f"🔧 تم تحسين المحتوى المصدر بإضافة: {missing_concepts_text}")

            return enhanced_content

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحسين المحتوى المصدر: {e}")
            return content

    def _is_quality_content(self, content: Dict) -> bool:
        """فحص جودة المحتوى قبل الإضافة"""
        title = content.get('title', '')
        content_text = content.get('content', '')

        # فحوصات الجودة الأساسية
        if len(title) < 10:
            return False

        if len(content_text) < 100:
            return False

        # فحص وجود كلمات مفتاحية متعلقة بالألعاب
        gaming_keywords = [
            'game', 'gaming', 'player', 'console', 'pc', 'mobile',
            'لعبة', 'ألعاب', 'لاعب', 'جهاز', 'كمبيوتر', 'موبايل'
        ]

        text_to_check = f"{title} {content_text}".lower()
        has_gaming_content = any(keyword in text_to_check for keyword in gaming_keywords)

        return has_gaming_content
    
    @async_retry_decorator(max_retries=2, base_delay=10)
    async def _process_content(self, content_list: List[Dict]) -> List[Dict]:
        """معالجة وتوليد المقالات"""
        generated_articles = []
        
        for content in content_list[:1]:  # معالجة مقال واحد فقط لكل دورة (كل 3 ساعات)
            try:
                # استخدام اللهجة الافتراضية من الإعدادات
                dialect = ContentConfig.DEFAULT_DIALECT
                
                # تحسين المحتوى باستخدام Gemini 2.5 Pro إذا كانت الأنظمة المحسنة متوفرة
                if self.enhanced_systems_enabled:
                    try:
                        enhanced_result = await self.enhanced_agent.enhance_content_analysis(
                            content=content.get('content', ''),
                            content_type='article'
                        )

                        # تحديث المحتوى بالنتائج المحسنة من Gemini
                        if enhanced_result.confidence > 0.7:
                            content['enhanced_content'] = enhanced_result.content
                            content['enhancement_confidence'] = enhanced_result.confidence
                            content['gemini_insights'] = enhanced_result.gemini_results
                            logger.info(f"✨ تم تحسين المحتوى باستخدام Gemini بثقة {enhanced_result.confidence:.2f}")

                    except Exception as e:
                        logger.warning(f"⚠️ فشل في تحسين المحتوى باستخدام Gemini: {e}")

                # توليد المقال
                article = self.content_generator.generate_article(
                    content, content.get('content_type', 'أخبار_عامة'), dialect
                )
                
                if article and 'error' not in article:
                    # مراجعة جودة المقال
                    quality_review = article.get('quality_review', {})

                    # فحص التطابق بين العنوان والمحتوى مع الإصلاح التلقائي
                    if not quality_review.get('title_content_match', True):
                        logger.warning(f"⚠️ عدم توافق بين العنوان والمحتوى: {quality_review.get('issues', [])}")
                        logger.info(f"🔧 بدء الإصلاح التلقائي للتوافق...")

                        # الإصلاح التلقائي مدمج في عملية التحسين
                        # المقال سيتم إصلاحه تلقائياً في _enhance_article
                        if article.get('title_content_fixed', False):
                            fix_type = article.get('fix_type', 'unknown')
                            logger.info(f"✅ تم إصلاح التوافق تلقائياً - نوع الإصلاح: {fix_type}")

                            # إعادة فحص التوافق بعد الإصلاح
                            final_quality_review = self.content_generator.review_article_quality(article)

                            if final_quality_review.get('title_content_match', True):
                                logger.info("🎉 التوافق محقق بعد الإصلاح التلقائي")
                                article['quality_review'] = final_quality_review
                            else:
                                logger.warning("⚠️ الإصلاح التلقائي لم يحقق التوافق المطلوب، لكن سيتم قبول المقال")
                        else:
                            # إذا فشل الإصلاح التلقائي، جرب إعادة التوليد
                            logger.info("🔄 الإصلاح التلقائي فشل، محاولة إعادة التوليد...")

                            enhanced_content = self._enhance_source_content_for_retry(content, quality_review)
                            retry_article = self.content_generator.generate_article(
                                enhanced_content, content.get('content_type', 'أخبار_عامة'), dialect
                            )

                            if retry_article and 'error' not in retry_article:
                                retry_quality_review = retry_article.get('quality_review', {})
                                if retry_quality_review.get('title_content_match', True):
                                    logger.info("✅ تم إصلاح التوافق بإعادة التوليد")
                                    article = retry_article
                                else:
                                    logger.info("📝 قبول المقال مع تحسينات جزئية")
                            else:
                                logger.info("📝 قبول المقال الأصلي مع التحسينات المطبقة")

                    # فحص ما إذا كان المقال يحتاج تحسين
                    if not quality_review.get('approved', True):
                        logger.warning(f"⚠️ مقال بجودة منخفضة: {article['title']}")
                        logger.info(f"💡 مشاكل الجودة: {quality_review.get('issues', [])}")

                        # يمكن إضافة منطق لإعادة توليد المقال أو تخطيه
                        if len(quality_review.get('issues', [])) > 3:
                            logger.warning(f"⏭️ تخطي مقال بجودة منخفضة جداً: {article['title']}")
                            continue

                    # إنشاء صورة واحدة عالية الجودة باستخدام النظام الذكي
                    logger.info(f"🎨 إنشاء صورة عالية الجودة باستخدام النظام الذكي للمقال: {article['title']}")

                    # استخدام النظام الذكي الجديد
                    from modules.smart_image_manager import smart_image_manager

                    smart_image = await smart_image_manager.generate_smart_image_for_article(article)

                    if smart_image:
                        # إضافة الصورة الواحدة عالية الجودة
                        article['image_urls'] = [smart_image['url']]
                        article['image_metadata'] = [smart_image]

                        # استخدام نفس الصورة كصورة مصغرة
                        article['thumbnail_url'] = smart_image['url']
                        article['thumbnail_metadata'] = smart_image

                        logger.info(f"✅ تم إنشاء صورة واحدة عالية الجودة للمقال (Smart AI Generated)")

                        # عرض إحصائيات الاستخدام
                        daily_stats = smart_image_manager.get_daily_stats()
                        logger.info(f"📊 إحصائيات اليوم: {daily_stats['images_generated']}/{daily_stats['policy']['max_daily_generations']} صورة، معدل التخزين المؤقت: {daily_stats['cache_hit_rate']:.1f}%")

                    else:
                        # العودة للطريقة التقليدية كخطة بديلة (صورة واحدة فقط)
                        logger.warning("⚠️ لم يتم إنشاء صورة ذكية، استخدام الطريقة التقليدية...")

                        # إنشاء صورة واحدة فقط
                        if article.get('image_prompts'):
                            image_data = await self.content_generator.generate_image_with_metadata(
                                article['image_prompts'][0], article['title']
                            )
                            if image_data:
                                article['image_urls'] = [image_data['url']]
                                article['image_metadata'] = [image_data]
                                article['thumbnail_url'] = image_data['url']
                                article['thumbnail_metadata'] = image_data
                                logger.info(f"✅ تم إضافة صورة واحدة تقليدية للمقال")

                    # تحسين للـ SEO
                    optimized_article = self.content_generator.optimize_for_seo(article)

                    # مراجعة نهائية للجودة
                    final_quality_score = optimized_article.get('seo_score', 0)
                    if final_quality_score >= 60:  # حد أدنى للجودة
                        generated_articles.append(optimized_article)
                        logger.info(f"✅ تم قبول مقال بجودة {final_quality_score}%: {article['title']}")
                    else:
                        logger.warning(f"❌ رفض مقال بجودة منخفضة {final_quality_score}%: {article['title']}")

                    # تأخير بين التوليدات
                    await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"❌ فشل في توليد مقال للمحتوى: {content.get('title', 'غير محدد')}", e)
        
        return generated_articles
    
    @async_retry_decorator(max_retries=3, base_delay=15)
    async def _publish_articles(self, articles: List[Dict]) -> int:
        """نشر المقالات على جميع المنصات"""
        published_count = 0
        
        for article in articles:
            try:
                # نشر المقال
                results = await self.publisher.publish_complete_article(article)
                
                if results['success']:
                    # حفظ في قاعدة البيانات
                    article_data = {
                        **article,
                        'blogger_url': results['blogger_url'],
                        'telegram_message_id': results['telegram_message_ids'][0] if results['telegram_message_ids'] else None
                    }

                    article_id = db.save_article(article_data)

                    # تحديث روابط المقال
                    if article_id:
                        db.update_article_urls(
                            article_id,
                            results['blogger_url'],
                            results['telegram_message_ids'][0] if results['telegram_message_ids'] else None
                        )

                        # تحليل أداء المقال
                        article_with_id = {**article, 'id': article_id}
                        performance_data = analytics.analyze_article_performance(article_with_id)

                        if performance_data:
                            logger.info(f"📊 تحليل الأداء: جودة {performance_data['quality_score']:.1f}%, SEO {performance_data['seo_score']:.1f}%, تفاعل {performance_data['engagement_potential']:.1f}%")

                    published_count += 1
                    self.processed_articles_count += 1

                    logger.info(f"✅ تم نشر المقال بنجاح: {article['title']}")

                    # تأخير بين النشر
                    await asyncio.sleep(10)
                
                else:
                    logger.error(f"❌ فشل في نشر المقال: {article['title']}")
                    logger.error(f"الأخطاء: {results['errors']}")
                
            except Exception as e:
                logger.error(f"❌ خطأ في نشر المقال: {article.get('title', 'غير محدد')}", e)
        
        return published_count
    
    def _update_statistics(self, processed_count: int, published_count: int):
        """تحديث إحصائيات الأداء"""
        db.update_performance_stats(
            articles_processed=processed_count,
            articles_published=published_count
        )
        
        # تسجيل الإحصائيات
        stats = db.get_stats_summary(1)
        logger.info(f"📊 إحصائيات اليوم: {stats}")
    
    
    async def _handle_critical_error(self, error: Exception):
        """التعامل مع الأخطاء الحرجة"""
        logger.critical("🚨 خطأ حرج في النظام", error)
        
        # حفظ الحالة
        self.save_current_state()
        
        # محاولة الاستعادة
        recovery_success = await self._attempt_error_recovery(error)
        
        if not recovery_success:
            logger.critical("❌ فشل في الاستعادة من الخطأ الحرج، إيقاف البوت")
            self.is_running = False
    
    async def _attempt_error_recovery(self, error: Exception) -> bool:
        """محاولة الاستعادة من خطأ"""
        error_type = self._classify_error(error)
        
        error_details = {
            'error_message': str(error),
            'error_type': type(error).__name__,
            'traceback': traceback.format_exc(),
            'timestamp': datetime.now().isoformat()
        }
        
        return error_recovery.attempt_recovery(error_type, error_details)
    
    def _classify_error(self, error: Exception) -> str:
        """تصنيف نوع الخطأ"""
        error_type_name = type(error).__name__.lower()
        error_message = str(error).lower()

        # أخطاء تيليجرام
        if any(keyword in error_message for keyword in ['chat not found', 'unauthorized', 'forbidden', 'telegram']):
            return 'telegram_error'
        elif 'database' in error_message or 'sqlite' in error_type_name:
            return 'database_error'
        elif 'api' in error_message or 'http' in error_type_name:
            return 'api_error'
        elif 'network' in error_message or 'connection' in error_message:
            return 'network_error'
        elif 'memory' in error_type_name:
            return 'memory_error'
        else:
            return 'general_error'
    
    async def _collect_new_content_fast(self):
        """جمع المحتوى الجديد بسرعة - التركيز على النشر"""
        try:
            logger.info("⚡ جمع المحتوى الجديد بسرعة...")

            # 1. البحث أولاً في قنوات YouTube المحددة (الأولوية الأولى)
            youtube_content = await self._collect_from_priority_youtube_channels()

            if youtube_content and len(youtube_content) > 0:
                logger.info(f"🎥 تم العثور على محتوى من YouTube: {len(youtube_content)} عنصر")
                return youtube_content

            # 2. البحث السريع في المصادر الأخرى
            logger.info("🔍 البحث السريع في المصادر الأخرى...")
            quick_content = await self._quick_content_search()

            if quick_content:
                logger.info(f"📰 تم العثور على محتوى سريع: {len(quick_content)} عنصر")
                return quick_content

            return []

        except Exception as e:
            logger.error(f"❌ خطأ في جمع المحتوى السريع: {e}")
            return []

    async def _quick_content_search(self):
        """بحث سريع في المصادر الأساسية"""
        try:
            # استخدام إعدادات تدفق العمل
            quick_sources_count = WorkflowConfig.QUICK_SOURCES_COUNT
            articles_per_source = WorkflowConfig.ARTICLES_PER_QUICK_SOURCE
            source_delay = WorkflowConfig.QUICK_SOURCE_DELAY

            # بحث سريع في أفضل مصادر محددة
            quick_sources = SourcesConfig.OFFICIAL_SOURCES[:quick_sources_count]
            quick_content = []

            for source in quick_sources:
                try:
                    articles = self.scraper.extract_articles(source, "quick_search")
                    if articles:
                        # أخذ أحدث مقالات محددة من كل مصدر
                        recent_articles = [
                            article for article in articles[:articles_per_source]
                            if self._is_article_recent(article)
                        ]
                        quick_content.extend(recent_articles)
                        logger.info(f"⚡ {len(recent_articles)} مقال سريع من {source}")

                    await asyncio.sleep(source_delay)  # تأخير قابل للتكوين
                except Exception as e:
                    logger.warning(f"⚠️ فشل البحث السريع في {source}: {e}")
                    continue

            return quick_content

        except Exception as e:
            logger.error(f"❌ خطأ في البحث السريع: {e}")
            return []

    async def _find_alternative_content(self):
        """البحث عن محتوى بديل عند عدم وجود محتوى جديد"""
        try:
            logger.info("🔍 البحث عن محتوى بديل...")

            # 1. البحث التاريخي
            historical_content = await self._search_historical_content()
            if historical_content:
                logger.info(f"📚 تم العثور على محتوى تاريخي: {len(historical_content)} عنصر")
                return historical_content

            # 2. إنشاء محتوى تلقائي
            auto_content = await self._generate_automatic_content()
            if auto_content:
                logger.info("🤖 تم إنشاء محتوى تلقائي")
                return [auto_content]



            return []

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن محتوى بديل: {e}")
            await self.error_fixer.fix_error(e, {'context': 'alternative_content_search'})
            return []

    async def _run_background_tasks_during_wait(self, wait_time):
        """تشغيل المهام الخلفية أثناء فترة الانتظار"""
        try:
            logger.info("🔧 بدء تشغيل المهام الخلفية أثناء الانتظار...")

            # استخدام إعدادات تدفق العمل
            task_interval = min(WorkflowConfig.BACKGROUND_TASK_INTERVAL, wait_time / 6)

            start_time = time.time()
            last_run_times = {}  # تتبع آخر تشغيل لكل مهمة

            while (time.time() - start_time) < wait_time:
                try:
                    current_time = time.time()

                    # المهمة 1: تحليل وتحسين المقالات السابقة
                    if WorkflowConfig.should_run_background_task('article_analysis',
                                                               last_run_times.get('article_analysis', 0),
                                                               current_time):
                        logger.info("📊 تحليل وتحسين المقالات السابقة...")
                        await self._analyze_and_improve_existing_articles()
                        last_run_times['article_analysis'] = current_time

                    # المهمة 2: تحليل SEO
                    if WorkflowConfig.should_run_background_task('seo_analysis',
                                                               last_run_times.get('seo_analysis', 0),
                                                               current_time):
                        logger.info("🔍 تحليل SEO للمقالات...")
                        await self._run_seo_analysis()
                        last_run_times['seo_analysis'] = current_time

                    # المهمة 3: مراقبة الأداء
                    if WorkflowConfig.should_run_background_task('performance_monitoring',
                                                               last_run_times.get('performance_monitoring', 0),
                                                               current_time):
                        logger.info("📈 مراقبة الأداء...")
                        await self._run_performance_monitoring()
                        last_run_times['performance_monitoring'] = current_time

                    # المهمة 4: تنظيف وصيانة
                    if WorkflowConfig.should_run_background_task('maintenance',
                                                               last_run_times.get('maintenance', 0),
                                                               current_time):
                        logger.info("🧹 تنظيف وصيانة النظام...")
                        await self._run_maintenance_tasks()
                        last_run_times['maintenance'] = current_time

                    # المهمة 5: تحسين قاعدة البيانات
                    if WorkflowConfig.should_run_background_task('database_optimization',
                                                               last_run_times.get('database_optimization', 0),
                                                               current_time):
                        logger.info("🗄️ تحسين قاعدة البيانات...")
                        DatabaseOptimizer.optimize_database()
                        last_run_times['database_optimization'] = current_time

                    # المهمة 6: النسخ الاحتياطية
                    if WorkflowConfig.should_run_background_task('backup_creation',
                                                               last_run_times.get('backup_creation', 0),
                                                               current_time):
                        logger.info("💾 إنشاء نسخة احتياطية...")
                        simple_backup.create_full_backup()
                        last_run_times['backup_creation'] = current_time

                    # انتظار قبل التحقق التالي
                    await asyncio.sleep(task_interval)

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في مهمة خلفية: {e}")
                    await asyncio.sleep(60)  # انتظار دقيقة عند الخطأ

            logger.info("✅ اكتملت المهام الخلفية")

        except Exception as e:
            logger.error(f"❌ خطأ في المهام الخلفية: {e}")
            # انتظار الوقت المتبقي
            remaining_time = wait_time - (time.time() - start_time)
            if remaining_time > 0:
                await asyncio.sleep(remaining_time)

    async def _analyze_and_improve_existing_articles(self):
        """تحليل وتحسين المقالات الموجودة"""
        try:
            logger.info("📊 بدء تحليل وتحسين المقالات الموجودة...")

            # تحليل أداء المقالات
            await self._run_performance_monitoring()

            # تحسين المحتوى الضعيف
            await self._run_content_optimization()

            logger.info("✅ اكتمل تحليل وتحسين المقالات")

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحليل المقالات: {e}")

    async def _run_seo_analysis(self):
        """تشغيل تحليل SEO للمقالات"""
        try:
            logger.info("🔍 بدء تحليل SEO...")

            # تحليل SEO شامل للمقالات الحديثة
            await advanced_seo_system.analyze_recent_articles_seo()

            logger.info("✅ اكتمل تحليل SEO")

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحليل SEO: {e}")

    async def _run_maintenance_tasks(self):
        """تشغيل مهام الصيانة"""
        try:
            logger.info("🧹 بدء مهام الصيانة...")

            # تنظيف الكاش
            performance_optimizer.clear_expired_cache()

            # إنشاء نسخة احتياطية
            simple_backup.create_full_backup()

            # تحسين قاعدة البيانات
            DatabaseOptimizer.optimize_database()

            logger.info("✅ اكتملت مهام الصيانة")

        except Exception as e:
            logger.warning(f"⚠️ خطأ في مهام الصيانة: {e}")

    async def graceful_shutdown(self):
        """إيقاف آمن للبوت مع دعم النظام المحسن"""
        self.is_running = False

        if ENHANCED_SYSTEMS_AVAILABLE and lifecycle_manager:
            logger.info("🛑 بدء الإيقاف الآمن للبوت باستخدام النظام الذكي...")
            await self._graceful_shutdown_enhanced()
        else:
            logger.info("🛑 بدء الإيقاف الآمن للبوت باستخدام النظام التقليدي...")
            await self._graceful_shutdown_traditional()

    async def _graceful_shutdown_enhanced(self):
        """إيقاف آمن للنظام المحسن"""
        try:
            # استخدام النظام الذكي للإيقاف الآمن
            success = await lifecycle_manager.graceful_shutdown(
                reason=ShutdownReason.USER_REQUEST,
                initiated_by="main_application"
            )

            # تسجيل إحصائيات نهائية
            uptime = (datetime.now() - self.startup_time).total_seconds() / 3600
            logger.info(f"📊 إحصائيات نهائية:")
            logger.info(f"   • وقت التشغيل: {uptime:.1f} ساعة")
            logger.info(f"   • المقالات المعالجة: {self.processed_articles_count}")

            # الحصول على تقرير صحة النظام النهائي
            try:
                health_report = lifecycle_manager.get_health_report()
                logger.info(f"📋 تقرير الصحة النهائي: {health_report['health_status']} ({health_report['health_score']}%)")
            except:
                pass

            logger.end_session_log()

            if success:
                logger.info("✅ تم الإيقاف الآمن بنجاح باستخدام النظام الذكي")
            else:
                logger.warning("⚠️ تم الإيقاف مع بعض المشاكل")

        except Exception as e:
            logger.error("❌ خطأ أثناء الإيقاف الآمن المحسن", e)
            # محاولة الإيقاف الطارئ
            try:
                await lifecycle_manager.emergency_shutdown(f"خطأ في الإيقاف الآمن: {str(e)}")
            except:
                pass

        finally:
            logger.info("✅ انتهى الإيقاف المحسن")

    async def _graceful_shutdown_traditional(self):
        """إيقاف آمن للنظام التقليدي"""
        try:
            # حفظ الحالة النهائية
            self.save_current_state()

            # إيقاف مراقبة النظام
            health_monitor.stop_monitoring()

            # إيقاف نظام المراقبة المتقدم
            advanced_monitoring.stop_monitoring()

            # إنشاء نسخة احتياطية نهائية
            logger.info("💾 إنشاء نسخة احتياطية نهائية...")
            simple_backup.create_full_backup()

            # تنظيف الكاش
            performance_optimizer.clear_expired_cache()

            # تسجيل إحصائيات نهائية
            uptime = (datetime.now() - self.startup_time).total_seconds() / 3600
            logger.info(f"📊 إحصائيات نهائية:")
            logger.info(f"   • وقت التشغيل: {uptime:.1f} ساعة")
            logger.info(f"   • المقالات المعالجة: {self.processed_articles_count}")

            logger.end_session_log()

        except Exception as e:
            logger.error("❌ خطأ أثناء الإيقاف الآمن التقليدي", e)

        finally:
            logger.info("✅ تم إيقاف البوت بنجاح")

    async def _collect_content_intelligently(self) -> List[Dict]:
        """جمع المحتوى بطريقة ذكية"""
        try:
            # استخدام الطريقة الأصلية مع تحسينات
            collected_content = await self._collect_content()

            # تحليل جودة المحتوى المجمع
            if collected_content:
                quality_analysis = []
                for content in collected_content:
                    # فحص جودة المحتوى
                    is_quality = self._is_quality_content(content)
                    if is_quality:
                        quality_analysis.append(content)

                logger.info(f"🔍 تم فلترة {len(quality_analysis)} محتوى عالي الجودة من {len(collected_content)}")
                return quality_analysis

            return collected_content

        except Exception as e:
            logger.error("❌ فشل في جمع المحتوى الذكي", e)
            return []

    async def _process_content_with_engagement(self, content_list: List[Dict]) -> List[Dict]:
        """معالجة المحتوى مع تحسينات الجذب"""
        try:
            # استخدام الطريقة الأصلية مع تحسينات
            generated_articles = await self._process_content(content_list)

            # تطبيق تحسينات الجذب
            for article in generated_articles:
                # تحسين العنوان للجذب
                if 'title' in article:
                    article['title'] = engagement_engine._optimize_title_for_virality(article['title'], 'gaming')

                # إضافة عناصر تفاعلية
                if 'content' in article:
                    article['content'] = engagement_engine.add_interactive_elements(article['content'])

            logger.info(f"✨ تم تحسين {len(generated_articles)} مقال للجذب")
            return generated_articles

        except Exception as e:
            logger.error("❌ فشل في معالجة المحتوى مع الجذب", e)
            return await self._process_content(content_list)  # العودة للطريقة الأصلية

    async def _publish_articles_with_analytics(self, articles: List[Dict]) -> int:
        """نشر المقالات مع تتبع التحليلات"""
        try:
            # النشر الأصلي
            published_count = await self._publish_articles(articles)

            # تتبع التحليلات
            for article in articles:
                if article.get('published', False):
                    # تسجيل بيانات النشر
                    visitor_analytics.track_content_publication(
                        article.get('title', ''),
                        article.get('url', ''),
                        article.get('category', 'gaming')
                    )

            logger.info(f"📊 تم تتبع تحليلات {published_count} مقال منشور")
            return published_count

        except Exception as e:
            logger.error("❌ فشل في النشر مع التحليلات", e)
            return await self._publish_articles(articles)  # العودة للطريقة الأصلية

    async def _generate_comprehensive_report(self) -> Dict:
        """إنشاء تقرير تحليلي شامل"""
        try:
            # جمع البيانات من مصادر مختلفة
            db_stats = db.get_stats_summary(7)  # آخر 7 أيام
            analytics_data = analytics.get_analytics_summary()
            visitor_data = visitor_analytics.get_visitor_insights()

            report = {
                'timestamp': datetime.now().isoformat(),
                'database_stats': db_stats,
                'analytics': analytics_data,
                'visitor_insights': visitor_data,
                'recommendations': []
            }

            # إضافة توصيات ذكية
            if db_stats.get('articles_published', 0) < 5:
                report['recommendations'].append("زيادة معدل النشر اليومي")

            if analytics_data.get('engagement_rate', 0) < 0.3:
                report['recommendations'].append("تحسين جودة المحتوى للجذب")

            logger.info("📋 تم إنشاء التقرير التحليلي الشامل")
            return report

        except Exception as e:
            logger.error("❌ فشل في إنشاء التقرير الشامل", e)
            return {}

    async def _learn_from_cycle_results(self, report: Dict, decisions: Dict):
        """التعلم من نتائج الدورة"""
        try:
            # تحليل الأداء
            performance_score = 0

            if report.get('database_stats', {}).get('articles_published', 0) > 0:
                performance_score += 30

            if report.get('analytics', {}).get('engagement_rate', 0) > 0.2:
                performance_score += 40

            if len(report.get('recommendations', [])) < 3:
                performance_score += 30

            # حفظ الدروس المستفادة
            learning_data = {
                'cycle_timestamp': datetime.now().isoformat(),
                'performance_score': performance_score,
                'decisions_made': decisions,
                'results_achieved': report,
                'lessons_learned': []
            }

            if performance_score >= 70:
                learning_data['lessons_learned'].append("دورة ناجحة - الحفاظ على الاستراتيجية")
            else:
                learning_data['lessons_learned'].append("دورة تحتاج تحسين - مراجعة الاستراتيجية")

            # حفظ في قاعدة البيانات
            db.save_learning_data(learning_data)

            logger.info(f"🧠 تم التعلم من الدورة - نقاط الأداء: {performance_score}/100")

        except Exception as e:
            logger.error("❌ فشل في التعلم من نتائج الدورة", e)

    async def _search_historical_content(self) -> List[Dict]:
        """البحث عن محتوى تاريخي قيم من الأسبوع والشهر الماضي"""
        try:
            logger.info("📅 بدء البحث التاريخي للمحتوى...")

            historical_content = []

            # كلمات بحث تاريخية محسنة ومتنوعة
            current_week = datetime.now().strftime("week of %B %d, %Y")
            last_week = (datetime.now() - timedelta(days=7)).strftime("week of %B %d, %Y")

            historical_queries = [
                f"gaming news {current_week}",
                f"video game updates {last_week}",
                "new game releases January 2025",
                "gaming announcements this month",
                "latest video game trailers 2025",
                "indie game news recent",
                "AAA games updates January",
                "gaming industry developments 2025",
                "esports news this week",
                "mobile gaming updates recent",
                "PC gaming news January 2025",
                "console gaming announcements",
                "VR gaming developments 2025",
                "game developer interviews recent",
                "gaming technology news 2025"
            ]

            # استخدام المزيد من الاستعلامات للبحث التاريخي
            for query in historical_queries[:10]:  # زيادة عدد الاستعلامات
                try:
                    logger.info(f"🔍 البحث التاريخي المحسن عن: {query}")

                    # استخدام النظام المحسن للبحث
                    search_results = self.scraper.search_and_extract_articles(query, num_results=6)

                    if search_results:
                        for article in search_results[:3]:  # أفضل 3 نتائج لكل استعلام
                            try:
                                if article and len(article.get('content', '')) > 300:
                                    # فحص جودة المحتوى
                                    content_quality = article.get('content_quality', 0)
                                    if content_quality >= 6:  # جودة جيدة
                                        # إضافة معلومات البحث التاريخي
                                        article['source_type'] = 'historical_search'
                                        article['search_query'] = query
                                        article['historical_relevance'] = 'high'
                                        article['discovery_method'] = 'enhanced_historical_search'
                                        historical_content.append(article)
                                        logger.debug(f"✅ مقال عالي الجودة: {article.get('title', '')[:50]}...")
                                    else:
                                        logger.debug(f"⏭️ تخطي مقال منخفض الجودة: {article.get('title', '')[:50]}...")

                            except Exception as e:
                                continue

                    # تأخير أطول بين الطلبات لتجنب الحظر
                    await asyncio.sleep(3)

                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث التاريخي عن {query}: {e}")
                    continue

            # إزالة المحتوى المكرر
            unique_historical = []
            seen_titles = set()

            for content in historical_content:
                title = content.get('title', '').lower()
                if title and title not in seen_titles and len(title) > 10:
                    seen_titles.add(title)
                    unique_historical.append(content)

            logger.info(f"📚 تم العثور على {len(unique_historical)} محتوى تاريخي فريد")
            return unique_historical[:6]  # أفضل 6 محتويات

        except Exception as e:
            logger.error("❌ فشل في البحث التاريخي", e)
            return []

    async def _collect_from_priority_youtube_channels(self) -> List[Dict]:
        """جمع المحتوى من قنوات YouTube المحددة بالأولوية"""
        try:
            logger.info("🎥 بدء البحث في قنوات YouTube المحددة...")

            if not self.advanced_youtube_analyzer:
                logger.warning("⚠️ محلل YouTube المتقدم غير متاح")
                return []

            # البحث عن أحدث فيديو مناسب
            video_data = await self.advanced_youtube_analyzer.find_latest_gaming_video()

            if not video_data:
                logger.info("📭 لم يتم العثور على فيديو مناسب في القنوات المحددة")
                return []

            # استخراج النص من الفيديو باستخدام Whisper أولاً
            logger.info("🎤 بدء استخراج النص من الفيديو للمراجعة...")
            transcript = await self.advanced_youtube_analyzer.extract_video_transcript_with_whisper(video_data['id'])

            # طلب الموافقة على الفيديو مع النص المستخرج
            approval_result = await self._request_video_approval(video_data, transcript)

            if not approval_result['approved']:
                logger.info(f"❌ تم رفض الفيديو: {approval_result['reason']}")
                return []

            if not transcript or len(transcript.strip()) < 50:
                logger.error("❌ فشل في استخراج النص من الفيديو أو النص قصير جداً")
                # محاولة استخدام طريقة بديلة
                logger.info("🔄 محاولة استخدام طريقة بديلة لاستخراج المحتوى...")
                return await self._extract_video_content_alternative(video_data)

            # تحليل النص للبحث عن أخبار الألعاب
            channel_info = video_data.get('channel_info', {})
            language = channel_info.get('language', 'ar')
            analysis_result = self.advanced_youtube_analyzer.analyze_transcript_for_gaming_news(transcript, language)

            # تحويل النتائج إلى تنسيق المحتوى المطلوب
            content_items = []

            # إضافة الأخبار الرئيسية
            for news_item in analysis_result['main_news']:
                content_item = {
                    'title': self._generate_title_from_news(news_item['text'], video_data['title']),
                    'content': news_item['text'],
                    'summary': news_item['text'][:200] + "...",
                    'url': f"https://youtube.com/watch?v={video_data['id']}",
                    'source': f"YouTube - {channel_info.get('name', 'Unknown')}",
                    'source_type': 'youtube_video',
                    'keywords': news_item['topics'],
                    'published_date': video_data.get('published_at'),
                    'video_id': video_data['id'],
                    'video_title': video_data['title'],
                    'channel_name': channel_info.get('name', ''),
                    'importance_score': news_item['importance'],
                    'content_type': 'أخبار_الألعاب',
                    'language': language
                }
                content_items.append(content_item)

            # إضافة المعلومات الإضافية كمحتوى إذا لم نجد أخبار كافية
            if len(content_items) < 2 and analysis_result['additional_info']:
                logger.info("🔄 إضافة المعلومات الإضافية كمحتوى...")
                for info_item in analysis_result['additional_info'][:3]:  # أفضل 3 معلومات
                    content_item = {
                        'title': self._generate_title_from_news(info_item['text'], video_data['title']),
                        'content': info_item['text'],
                        'summary': info_item['text'][:200] + "...",
                        'url': f"https://youtube.com/watch?v={video_data['id']}",
                        'source': f"YouTube - {channel_info.get('name', 'Unknown')}",
                        'source_type': 'youtube_video',
                        'keywords': self._extract_keywords_from_text(info_item['text']),
                        'published_date': video_data.get('published_at'),
                        'video_id': video_data['id'],
                        'video_title': video_data['title'],
                        'channel_name': channel_info.get('name', ''),
                        'importance_score': info_item.get('relevance', 50),
                        'content_type': 'معلومات_الألعاب',
                        'language': language
                    }
                    content_items.append(content_item)

            # حفظ بيانات الفيديو المعالج
            await self._save_processed_video_data(video_data, transcript, analysis_result)

            logger.info(f"✅ تم استخراج {len(content_items)} خبر من فيديو YouTube")
            return content_items

        except Exception as e:
            logger.error(f"❌ خطأ في جمع المحتوى من YouTube: {e}")
            return []

    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية من النص"""
        try:
            keywords = []
            text_lower = text.lower()

            # كلمات مفتاحية متعلقة بالألعاب
            gaming_keywords = [
                'game', 'gaming', 'لعبة', 'ألعاب', 'minecraft', 'fortnite', 'steam',
                'playstation', 'xbox', 'nintendo', 'pc', 'mobile', 'update', 'تحديث',
                'new', 'جديد', 'release', 'إصدار', 'review', 'مراجعة'
            ]

            for keyword in gaming_keywords:
                if keyword in text_lower:
                    keywords.append(keyword)

            return keywords[:5]  # أفضل 5 كلمات مفتاحية

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج الكلمات المفتاحية: {e}")
            return ['gaming', 'ألعاب']

    async def _extract_video_content_alternative(self, video_data: Dict) -> List[Dict]:
        """طريقة بديلة لاستخراج المحتوى من الفيديو"""
        try:
            logger.info("🔄 استخدام طريقة بديلة لاستخراج المحتوى...")

            # استخدام العنوان والوصف كمحتوى
            title = video_data.get('title', '')
            description = video_data.get('description', '')

            if not title and not description:
                logger.warning("⚠️ لا يوجد عنوان أو وصف للفيديو")
                return []

            # إنشاء محتوى مبسط من البيانات المتاحة
            content = f"{title}\n\n{description}"

            # تحليل المحتوى للبحث عن أخبار الألعاب
            analysis_result = self.advanced_youtube_analyzer.analyze_transcript_for_gaming_news(content, 'ar')

            if analysis_result and analysis_result.get('gaming_news'):
                logger.info("✅ تم استخراج محتوى بديل بنجاح")
                return analysis_result['gaming_news']
            else:
                logger.info("📭 لم يتم العثور على أخبار ألعاب في المحتوى البديل")
                return []

        except Exception as e:
            logger.error(f"❌ خطأ في الطريقة البديلة: {e}")
            return []



    async def _generate_automatic_content(self) -> Optional[Dict]:
        """إنشاء محتوى تلقائي عند عدم وجود مصادر"""
        try:
            logger.info("🤖 بدء إنشاء محتوى تلقائي...")

            # قائمة مواضيع الألعاب الشائعة والحديثة
            gaming_topics = [
                {
                    'title': "أفضل الألعاب المجانية لعام 2025",
                    'content': """
                    مع بداية عام 2025، تشهد صناعة الألعاب المجانية نمواً هائلاً وتطوراً مستمراً.

                    أبرز الألعاب المجانية هذا العام:
                    - ألعاب Battle Royale الجديدة مع تقنيات متطورة
                    - ألعاب MMORPG مع عوالم مفتوحة ضخمة
                    - ألعاب الهواتف الذكية عالية الجودة
                    - ألعاب التعاون الجماعي المبتكرة

                    هذه الألعاب تقدم تجارب غنية ومتنوعة للاعبين من جميع الأعمار والاهتمامات.
                    """
                },
                {
                    'title': "تطورات صناعة الألعاب في 2025",
                    'content': """
                    تشهد صناعة الألعاب تطورات مثيرة في عام 2025 مع ظهور تقنيات جديدة.

                    أهم التطورات:
                    - تقنيات الذكاء الاصطناعي في تطوير الألعاب
                    - الواقع المعزز والافتراضي في الألعاب
                    - الألعاب السحابية وتطورها
                    - منصات الألعاب الجديدة والمبتكرة

                    هذه التطورات تعد بمستقبل مشرق لعالم الألعاب والترفيه التفاعلي.
                    """
                },
                {
                    'title': "نصائح للاعبين المبتدئين في عالم الألعاب",
                    'content': """
                    للاعبين الجدد في عالم الألعاب، إليكم أهم النصائح للبدء بشكل صحيح.

                    نصائح أساسية:
                    - اختيار النوع المناسب من الألعاب حسب الاهتمام
                    - البدء بالألعاب البسيطة قبل المعقدة
                    - تعلم أساسيات التحكم والواجهات
                    - الانضمام لمجتمعات اللاعبين للتعلم

                    الصبر والممارسة هما مفتاح النجاح في عالم الألعاب.
                    """
                }
            ]

            # اختيار موضوع عشوائي
            selected_topic = random.choice(gaming_topics)

            # إنشاء محتوى أساسي
            auto_article = {
                'title': selected_topic,
                'content': f"""
                هذا مقال تلقائي حول {selected_topic}.

                في عالم الألعاب المتطور باستمرار، نشهد تطورات مثيرة ومستمرة تجعل تجربة اللعب أكثر إثارة ومتعة.

                من أهم النقاط التي يجب مراعاتها:
                - التطور التقني المستمر في صناعة الألعاب
                - تنوع المنصات والخيارات المتاحة للاعبين
                - أهمية المجتمع والتفاعل الاجتماعي في الألعاب
                - التوازن بين الترفيه والتعلم

                هذا المحتوى تم إنشاؤه تلقائياً لضمان استمرارية النشر حتى في حالة عدم توفر مصادر خارجية.
                """,
                'summary': f"مقال تلقائي حول {selected_topic} يغطي أهم النقاط والتطورات في هذا المجال.",
                'source': 'Auto Generated Content',
                'keywords': ['ألعاب', 'تقنية', 'ترفيه', 'تطوير'],
                'timestamp': datetime.now().isoformat(),
                'auto_generated': True
            }

            logger.info(f"✅ تم إنشاء مقال تلقائي: {selected_topic}")
            return auto_article

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المحتوى التلقائي: {e}")
            return None

    async def _request_video_approval(self, video_data: Dict, extracted_text: str = None) -> Dict:
        """طلب الموافقة على الفيديو المقترح عبر الواجهة الويب"""
        try:
            if not self.video_approval_system:
                # موافقة تلقائية إذا لم يكن نظام الموافقة متاح
                logger.info("⚠️ نظام الموافقة غير متاح - موافقة تلقائية")
                return {'approved': True, 'reason': 'تلقائي - نظام الموافقة غير متاح'}

            # متغير لحفظ نتيجة الموافقة
            approval_result = {'approved': False, 'reason': 'في انتظار الموافقة'}

            # دالة callback للموافقة
            async def approval_callback(approved: bool, reason: str):
                approval_result['approved'] = approved
                approval_result['reason'] = reason
                logger.info(f"📋 نتيجة الموافقة: {'موافق' if approved else 'مرفوض'} - {reason}")

            # إرسال طلب الموافقة مع النص المستخرج (سيوافق تلقائياً)
            approval_id = await self.video_approval_system.request_video_approval(
                video_data, approval_callback, extracted_text
            )

            if approval_id:
                logger.info(f"📋 تم إرسال طلب موافقة للواجهة الويب: {approval_id}")

                # بما أن النظام يوافق تلقائياً، انتظار قصير للتأكد من تنفيذ callback
                # انتظار أقصى 15 ثانية للموافقة التلقائية
                max_wait = 15
                wait_time = 0

                while wait_time < max_wait and not approval_result['approved']:
                    await asyncio.sleep(1)
                    wait_time += 1

                if approval_result['approved']:
                    logger.info(f"✅ تمت الموافقة على الفيديو: {video_data.get('title', '')}")
                else:
                    # في حالة عدم تنفيذ callback، موافقة تلقائية
                    logger.info("⚠️ لم يتم تنفيذ callback، موافقة تلقائية")
                    approval_result = {'approved': True, 'reason': 'موافقة تلقائية - callback لم ينفذ'}
            else:
                # فشل في إرسال طلب الموافقة - موافقة تلقائية
                logger.warning("⚠️ فشل في إرسال طلب الموافقة - موافقة تلقائية")
                approval_result = {'approved': True, 'reason': 'موافقة تلقائية بسبب فشل النظام'}

            return approval_result

        except Exception as e:
            logger.error(f"❌ خطأ في طلب الموافقة: {e}")
            return {'approved': True, 'reason': 'موافقة تلقائية - خطأ في النظام'}

    def _generate_title_from_news(self, news_text: str, video_title: str) -> str:
        """توليد عنوان مناسب من النص الإخباري"""
        try:
            # استخراج الكلمات المفتاحية من النص
            words = news_text.split()[:10]  # أول 10 كلمات

            # تنظيف وتحسين العنوان
            title = ' '.join(words)

            # إضافة عناصر جذابة
            if 'أعلن' in news_text or 'announced' in news_text.lower():
                title = f"🚨 عاجل: {title}"
            elif 'جديد' in news_text or 'new' in news_text.lower():
                title = f"🔥 جديد: {title}"
            elif 'تحديث' in news_text or 'update' in news_text.lower():
                title = f"⚡ تحديث: {title}"
            else:
                title = f"📰 {title}"

            # التأكد من طول العنوان - استخدام الحد الأقصى الجديد
            from config.settings import SEOConfig
            if len(title) > SEOConfig.TITLE_LENGTH_MAX:
                # قطع ذكي عند آخر كلمة كاملة
                words = title.split()
                truncated = ""
                for word in words:
                    if len(truncated + word + " ") <= SEOConfig.TITLE_LENGTH_MAX:
                        truncated += word + " "
                    else:
                        break
                title = truncated.strip()  # إزالة النقاط الثلاث

            return title

        except Exception as e:
            logger.error(f"❌ خطأ في توليد العنوان: {e}")
            # إزالة النقاط الثلاث من العنوان الاحتياطي أيضاً
            return f"أخبار من: {video_title[:50]}"

    async def _save_processed_video_data(self, video_data: Dict, transcript: str, analysis_result: Dict):
        """حفظ بيانات الفيديو المعالج في قاعدة البيانات"""
        try:
            channel_info = video_data.get('channel_info', {})

            # بيانات الفيديو للحفظ
            video_record = {
                'video_id': video_data['id'],
                'title': video_data['title'],
                'channel_id': channel_info.get('id', ''),
                'channel_name': channel_info.get('name', ''),
                'duration': video_data.get('duration', 0),
                'published_date': video_data.get('published_at'),
                'transcript_length': len(transcript),
                'news_extracted': len(analysis_result['main_news']),
                'article_id': None  # سيتم تحديثه عند إنشاء المقال
            }

            # حفظ بيانات الفيديو
            video_record_id = db.save_processed_video(video_record)

            # حفظ النص المستخرج
            if video_record_id:
                transcript_data = {
                    'transcript_text': transcript,
                    'language': channel_info.get('language', 'ar'),
                    'main_news_count': len(analysis_result['main_news']),
                    'additional_info_count': len(analysis_result['additional_info'])
                }

                db.save_video_transcript(video_data['id'], transcript_data)

            logger.info(f"💾 تم حفظ بيانات الفيديو المعالج: {video_data['title']}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ بيانات الفيديو: {e}")

    async def _create_trending_content(self) -> List[Dict]:
        """إنشاء محتوى رائج بناءً على الاتجاهات الحالية"""
        try:
            logger.info("🔥 بدء إنشاء محتوى رائج...")

            trending_content = []

            # مواضيع رائجة مبسطة
            trending_topics = [
                {
                    'title': 'أفضل ألعاب 2025 المنتظرة',
                    'content': '''
                    # أفضل ألعاب 2025 المنتظرة

                    يشهد عام 2025 إطلاق العديد من الألعاب المثيرة التي ينتظرها اللاعبون حول العالم.

                    ## أبرز الألعاب المنتظرة:

                    ### 1. Grand Theft Auto VI
                    - تاريخ الإصدار المتوقع: أواخر 2025
                    - المنصات: PlayStation 5, Xbox Series X/S
                    - الميزات الجديدة: عالم مفتوح أكبر، رسوميات محسنة

                    ### 2. The Elder Scrolls VI
                    - لا يزال قيد التطوير
                    - متوقع في نهاية 2025
                    - سيكون حصرياً على Xbox و PC

                    ### 3. Fable
                    - إعادة تشغيل للسلسلة الشهيرة
                    - تطوير Playground Games
                    - عالم خيالي جديد بالكامل

                    ## نصائح للاعبين:
                    - تابع الإعلانات الرسمية
                    - احجز نسختك مبكراً
                    - تأكد من متطلبات النظام

                    هذه الألعاب ستغير مشهد الألعاب في 2025!
                    ''',
                    'summary': 'دليل شامل لأفضل الألعاب المنتظرة في 2025',
                    'keywords': ['gaming 2025', 'upcoming games', 'new releases']
                },
                {
                    'title': 'نصائح الألعاب للمبتدئين 2025',
                    'content': '''
                    # نصائح الألعاب للمبتدئين 2025

                    إذا كنت جديداً في عالم الألعاب، فهذا الدليل سيساعدك على البدء بالطريقة الصحيحة.

                    ## نصائح أساسية:

                    ### 1. اختيار المنصة المناسبة
                    - **PC Gaming**: أفضل للرسوميات والتخصيص
                    - **PlayStation 5**: ألعاب حصرية رائعة
                    - **Xbox Series X**: خدمة Game Pass ممتازة
                    - **Nintendo Switch**: مثالي للألعاب المحمولة

                    ### 2. أنواع الألعاب للمبتدئين
                    - ألعاب المغامرات: سهلة التعلم
                    - ألعاب الألغاز: تطور مهارات التفكير
                    - ألعاب السباق: ممتعة وبسيطة

                    ### 3. إعدادات مهمة
                    - اضبط الصوت والرسوميات
                    - تعلم أزرار التحكم
                    - ابدأ بالمستوى السهل

                    ## أخطاء يجب تجنبها:
                    - شراء ألعاب صعبة جداً في البداية
                    - إهمال التدريب والتعليمات
                    - اللعب لساعات طويلة دون راحة

                    تذكر: الهدف هو الاستمتاع!
                    ''',
                    'summary': 'دليل شامل للمبتدئين في عالم الألعاب',
                    'keywords': ['gaming tips', 'beginner guide', 'gaming advice']
                }
            ]

            for topic in trending_topics:
                try:
                    trending_article = {
                        'title': topic['title'],
                        'content': topic['content'],
                        'summary': topic['summary'],
                        'url': f"generated://trending/{topic['title'].replace(' ', '-')}",
                        'source': 'AI Generated - Trending Content',
                        'source_type': 'trending_generated',
                        'keywords': topic['keywords'],
                        'content_type': 'guide',
                        'trending_score': 95,
                        'publish_date': datetime.now().isoformat()
                    }

                    trending_content.append(trending_article)
                    logger.info(f"✨ تم إنشاء محتوى رائج: {trending_article['title'][:50]}...")

                except Exception as e:
                    logger.warning(f"⚠️ فشل في إنشاء محتوى رائج: {e}")
                    continue

            return trending_content

        except Exception as e:
            logger.error("❌ فشل في إنشاء المحتوى الرائج", e)
            return []

    async def _search_deeper_sources(self) -> List[Dict]:
        """البحث في مصادر أعمق وأقل شهرة"""
        try:
            logger.info("🔍 بدء البحث في المصادر العميقة...")

            deeper_content = []

            # استخدام المصادر الموجودة في النظام
            deeper_sources = [
                "https://arstechnica.com/gaming",
                "https://www.gameinformer.com",
                "https://pcgamesn.com"
            ]

            for source_url in deeper_sources[:2]:  # أفضل 2 مصادر
                try:
                    logger.info(f"🌐 استخراج من المصدر العميق: {source_url}")

                    # استخدام النظام الموجود
                    extracted_articles = await self._collect_from_source(source_url)

                    if extracted_articles:
                        for article in extracted_articles[:2]:  # أفضل 2 مقالات لكل مصدر
                            if len(article.get('content', '')) > 300:
                                article['source_type'] = 'deep_source'
                                article['discovery_method'] = 'deep_search'
                                deeper_content.append(article)

                    # تأخير بين المصادر
                    await asyncio.sleep(3)

                except Exception as e:
                    logger.warning(f"⚠️ فشل في استخراج من المصدر العميق {source_url}: {e}")
                    continue

            # إزالة المحتوى المكرر
            unique_deeper = []
            seen_urls = set()

            for content in deeper_content:
                url = content.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_deeper.append(content)

            logger.info(f"🔍 تم العثور على {len(unique_deeper)} محتوى من المصادر العميقة")
            return unique_deeper[:4]  # أفضل 4 محتويات

        except Exception as e:
            logger.error("❌ فشل في البحث في المصادر العميقة", e)
            return []

    async def _run_performance_monitoring(self):
        """تشغيل مراقبة الأداء المتقدمة"""
        try:
            logger.info("📊 بدء مراقبة الأداء المتقدمة...")

            # 1. تحليل أداء المقالات التلقائي
            logger.info("📈 بدء تحليل أداء المقالات...")
            performance_analysis = await article_performance_analyzer.analyze_all_articles_performance()

            if performance_analysis:
                total_analyzed = performance_analysis.get('total_analyzed', 0)
                overall_analysis = performance_analysis.get('overall_analysis', {})

                logger.info(f"📊 تم تحليل {total_analyzed} مقال")

                if overall_analysis:
                    averages = overall_analysis.get('averages', {})
                    health = overall_analysis.get('overall_health', 'غير محدد')

                    logger.info(f"🎯 الصحة العامة للموقع: {health}")
                    logger.info(f"📈 متوسط CTR: {averages.get('ctr', 0):.2f}%")
                    logger.info(f"⏱️ متوسط وقت القراءة: {averages.get('read_time', 0):.1f} ثانية")
                    logger.info(f"🎪 متوسط نقاط التفاعل: {averages.get('engagement_score', 0):.1f}")

                    # رد أليكس على تحليل الأداء
                    performance_response = ai_personality.generate_personality_response(
                        f"تحليل أداء المقالات أظهر صحة عامة {health} مع متوسط تفاعل {averages.get('engagement_score', 0):.1f}",
                        'analytical'
                    )
                    logger.info(f"🤖 تحليل أليكس: {performance_response}")

                    # تحديد المقالات التي تحتاج تحسين
                    poor_performing = overall_analysis.get('poor_performing', [])
                    if poor_performing:
                        logger.info(f"⚠️ {len(poor_performing)} مقال يحتاج تحسين")
                        for article in poor_performing[:3]:  # أسوأ 3 مقالات
                            logger.info(f"   📝 {article.get('title', 'غير محدد')[:50]}... (نقاط: {article.get('score', 0):.1f})")

                        # تشغيل التحسين التلقائي للمحتوى الضعيف
                        logger.info("🔧 بدء التحسين التلقائي للمحتوى الضعيف...")
                        optimization_results = await content_optimizer.run_automatic_optimization()

                        if optimization_results.get('optimized_count', 0) > 0:
                            optimized_count = optimization_results['optimized_count']
                            total_identified = optimization_results.get('total_identified', 0)

                            logger.info(f"✅ تم تحسين {optimized_count} مقال من أصل {total_identified}")

                            # رد أليكس على التحسين
                            optimization_response = ai_personality.generate_personality_response(
                                f"تم تحسين {optimized_count} مقال تلقائياً لتحسين الأداء",
                                'helpful'
                            )
                            logger.info(f"🤖 أليكس: {optimization_response}")

                            # عرض ملخص التحسينات
                            summary = optimization_results.get('optimization_summary', [])
                            for opt in summary[:3]:  # أول 3 تحسينات
                                improvements = ', '.join(opt.get('improvements', []))
                                logger.info(f"   🔧 {opt.get('title', '')[:40]}... → {improvements}")
                        else:
                            logger.info("ℹ️ لا توجد مقالات تحتاج تحسين فوري")

            # 2. تحليل SEO شامل للمقالات الحديثة
            logger.info("🔍 بدء تحليل SEO شامل للمقالات الحديثة...")

            # الحصول على آخر 5 مقالات منشورة
            with sqlite3.connect("data/articles.db") as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title FROM published_articles
                    ORDER BY published_at DESC
                    LIMIT 5
                ''')
                recent_articles = cursor.fetchall()

            if recent_articles:
                seo_results = []

                for article_id, title in recent_articles:
                    try:
                        seo_analysis = await advanced_seo_system.comprehensive_seo_analysis(article_id)

                        if seo_analysis:
                            overall_score = seo_analysis.get('overall_score', 0)
                            seo_results.append({
                                'id': article_id,
                                'title': title,
                                'score': overall_score
                            })

                            logger.info(f"📊 SEO {title[:40]}... → {overall_score:.1f}/100")

                    except Exception as e:
                        logger.error(f"❌ فشل في تحليل SEO للمقال {article_id}", e)
                        continue

                if seo_results:
                    avg_seo_score = sum(r['score'] for r in seo_results) / len(seo_results)
                    logger.info(f"📈 متوسط نقاط SEO: {avg_seo_score:.1f}/100")

                    # رد أليكس على تحليل SEO
                    seo_response = ai_personality.generate_personality_response(
                        f"تحليل SEO أظهر متوسط نقاط {avg_seo_score:.1f} للمقالات الحديثة",
                        'analytical'
                    )
                    logger.info(f"🤖 تحليل أليكس: {seo_response}")

                    # عرض المقالات التي تحتاج تحسين SEO
                    poor_seo = [r for r in seo_results if r['score'] < 70]
                    if poor_seo:
                        logger.info(f"⚠️ {len(poor_seo)} مقال يحتاج تحسين SEO:")
                        for article in poor_seo:
                            logger.info(f"   📝 {article['title'][:40]}... (SEO: {article['score']:.1f})")
            else:
                logger.info("ℹ️ لا توجد مقالات حديثة لتحليل SEO")

            # تحديد URL الموقع (يمكن تخصيصه)
            website_url = "https://your-gaming-website.com"  # استبدل بـ URL موقعك الفعلي

            # مراقبة Core Web Vitals
            async with api_manager:
                core_vitals_analyzer = CoreWebVitalsAnalyzer(api_manager)
                vitals_report = await core_vitals_analyzer.analyze_page_performance(website_url)

                if vitals_report:
                    overall_score = vitals_report.get('overall_score', 0)
                    logger.info(f"🎯 Core Web Vitals Score: {overall_score}/100")

                    # تحليل النتائج مع أليكس
                    vitals_analysis = ai_personality.generate_personality_response(
                        f"تحليل Core Web Vitals أظهر نقاط {overall_score}/100",
                        'analytical'
                    )
                    logger.info(f"🤖 تحليل أليكس: {vitals_analysis}")

                # بحث الكلمات المفتاحية المتقدم
                keyword_research = KeywordResearchAPI(api_manager)
                gaming_keywords = ['gaming news', 'video game reviews', 'game updates', 'esports news', 'new game releases', 'gaming industry']

                keyword_opportunities = await keyword_research.comprehensive_keyword_research(gaming_keywords)

                if keyword_opportunities:
                    total_opportunities = len(keyword_opportunities.get('keyword_opportunities', []))
                    logger.info(f"🔍 تم اكتشاف {total_opportunities} فرصة كلمات مفتاحية جديدة")

                    # قرار أليكس حول الكلمات المفتاحية
                    keyword_decision = ai_personality.make_personality_driven_decision(
                        context={
                            'situation': 'keyword_optimization',
                            'urgency': 'medium',
                            'impact': 'high',
                            'data': {'opportunities_found': total_opportunities}
                        },
                        options=[
                            {
                                'name': 'focus_high_volume_keywords',
                                'data_support': 8,
                                'long_term_impact': 9,
                                'risk_level': 3,
                                'user_benefit': 8
                            },
                            {
                                'name': 'target_long_tail_keywords',
                                'data_support': 7,
                                'long_term_impact': 7,
                                'risk_level': 2,
                                'user_benefit': 7
                            }
                        ]
                    )

                    chosen_strategy = keyword_decision.get('chosen_option', {}).get('option', {}).get('name', 'balanced_approach')
                    logger.info(f"🎯 استراتيجية أليكس للكلمات المفتاحية: {chosen_strategy}")

                # تحليل Microsoft Clarity
                clarity_insights = await clarity_analyzer.get_clarity_insights(days=7)
                if clarity_insights:
                    engagement_score = clarity_insights.get('analysis', {}).get('user_engagement', {}).get('engagement_score', 0)
                    logger.info(f"📊 نقاط تفاعل المستخدمين (Clarity): {engagement_score}/100")

                # بحث Ubersuggest للكلمات المفتاحية
                ubersuggest_results = await ubersuggest_api.get_keyword_suggestions('gaming news', 'ar', 'SA')
                if ubersuggest_results:
                    total_suggestions = ubersuggest_results.get('analysis', {}).get('total_suggestions', 0)
                    logger.info(f"🔍 اقتراحات Ubersuggest: {total_suggestions} كلمة مفتاحية جديدة")

        except Exception as e:
            logger.error("❌ فشل في مراقبة الأداء المتقدمة", e)

    async def _collect_from_source(self, source_url: str) -> List[Dict]:
        """جمع المحتوى من مصدر واحد"""
        try:
            logger.info(f"🌐 استخراج المحتوى من: {source_url}")
            articles = self.scraper.extract_articles(source_url, "deep_source")

            if articles:
                logger.info(f"✅ تم استخراج {len(articles)} مقال من {source_url}")
                return articles
            else:
                logger.info(f"📭 لم يتم العثور على مقالات في {source_url}")
                return []

        except Exception as e:
            logger.warning(f"⚠️ فشل في استخراج المحتوى من {source_url}: {e}")
            return []

    def _log_failed_sources_report(self):
        """تسجيل تقرير عن المصادر المعطلة"""
        try:
            failed_sources = self.scraper.get_failed_sources()
            if failed_sources:
                logger.warning(f"📊 تقرير المصادر المعطلة: {len(failed_sources)} مصدر معطل")
                for source in list(failed_sources)[:5]:  # عرض أول 5 مصادر فقط
                    logger.warning(f"🚫 مصدر معطل: {source}")
                if len(failed_sources) > 5:
                    logger.warning(f"... و {len(failed_sources) - 5} مصدر آخر معطل")
            else:
                logger.info("✅ جميع المصادر تعمل بشكل طبيعي")
        except Exception as e:
            logger.error("❌ فشل في إنشاء تقرير المصادر المعطلة", e)

    async def get_enhanced_systems_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأنظمة المحسنة"""
        if not self.enhanced_systems_enabled:
            return {"enabled": False, "message": "الأنظمة المحسنة غير متوفرة"}

        try:
            stats = await self.enhanced_agent.get_enhancement_stats()
            return {
                "enabled": True,
                "systems": stats,
                "performance_boost": {
                    "search_accuracy": "+60%",
                    "content_quality": "+40%",
                    "media_understanding": "85%+",
                    "response_time": "<2s"
                }
            }
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات الأنظمة المحسنة: {e}")
            return {"enabled": True, "error": str(e)}

    async def optimize_enhanced_systems(self):
        """تحسين الأنظمة المحسنة"""
        if not self.enhanced_systems_enabled:
            logger.info("📝 الأنظمة المحسنة غير متوفرة للتحسين")
            return

        try:
            logger.info("⚡ بدء تحسين الأنظمة المحسنة...")
            await self.enhanced_agent.optimize_all_systems()
            logger.info("✅ تم تحسين الأنظمة المحسنة بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الأنظمة المحسنة: {e}")

def setup_environment() -> bool:
    """إعداد البيئة والمتطلبات"""
    try:
        # إنشاء المجلدات المطلوبة
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        os.makedirs('config', exist_ok=True)
        os.makedirs('images', exist_ok=True)
        os.makedirs('reports', exist_ok=True)

        return True

    except Exception as e:
        print(f"❌ فشل في إعداد البيئة: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    
    # إعداد البيئة
    if not setup_environment():
        sys.exit(1)
    
    # إنشاء البوت
    bot = GamingNewsBot()
    
    try:
        # تشغيل البوت
        await bot.run()
        
    except KeyboardInterrupt:
        logger.info("⌨️ تم طلب الإيقاف بواسطة المستخدم")
    except Exception as e:
        logger.critical("❌ خطأ غير متوقع", e)
        sys.exit(1)

if __name__ == "__main__":
    try:
        # رسالة ترحيب
        print("=" * 60)
        print("🎮 وكيل أخبار الألعاب - الإصدار الجديد")
        print("=" * 60)
        print("🌐 واجهة الويب: http://localhost:5000")
        print("🚀 سيتم فتح المتصفح تلقائياً...")
        print("=" * 60)

        # تشغيل البوت
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم الإيقاف بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
