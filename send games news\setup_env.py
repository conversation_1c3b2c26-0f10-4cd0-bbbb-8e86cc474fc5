#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد متغيرات البيئة للبوت
يضمن وجود جميع المتغيرات المطلوبة
"""

import os
import sys

def setup_environment_variables():
    """إعداد متغيرات البيئة المطلوبة"""
    print("🔧 إعداد متغيرات البيئة...")
    
    # متغيرات البيئة الأساسية
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'RENDER': 'true',
        'RENDER_SERVICE_TYPE': 'web',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'LOG_LEVEL': 'INFO'
    }
    
    # تعيين المتغيرات
    set_count = 0
    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = value
            print(f"✅ تم تعيين {key}")
            set_count += 1
        else:
            print(f"ℹ️ {key} موجود بالفعل")
    
    print(f"🎯 تم تعيين {set_count} متغير بيئة")
    return True

def verify_environment():
    """التحقق من متغيرات البيئة"""
    print("🔍 التحقق من متغيرات البيئة...")
    
    required_vars = [
        'BOT_TOKEN',
        'TELEGRAM_BOT_TOKEN', 
        'SUPABASE_URL',
        'SUPABASE_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            # إخفاء جزء من القيمة للأمان
            value = os.getenv(var)
            masked_value = value[:10] + "..." if len(value) > 10 else value
            print(f"✅ {var}: {masked_value}")
    
    if missing_vars:
        print(f"❌ متغيرات مفقودة: {', '.join(missing_vars)}")
        return False
    
    print("✅ جميع متغيرات البيئة موجودة")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد البيئة للبوت")
    print("=" * 40)
    
    # إعداد المتغيرات
    setup_success = setup_environment_variables()
    
    # التحقق من المتغيرات
    verify_success = verify_environment()
    
    if setup_success and verify_success:
        print("\n🎉 تم إعداد البيئة بنجاح!")
        return True
    else:
        print("\n❌ فشل في إعداد البيئة")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
