# تحميل متغيرات البيئة أولاً
import os
from dotenv import load_dotenv

# تحميل ملف .env
load_dotenv()

import logging
import requests
from typing import List, Dict, Optional
import io
import base64
import uuid
from datetime import datetime

# إعداد التسجيل
logger = logging.getLogger(__name__)

# إعدادات Supabase - استخدام النظام الآمن
try:
    from secure_config import get_supabase_url, get_supabase_key
    SUPABASE_URL = get_supabase_url()
    SUPABASE_KEY = get_supabase_key()
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("إعدادات Supabase مفقودة من secure_config")
except (ImportError, ValueError):
    # Fallback للنظام القديم
    SUPABASE_URL = os.environ.get("SUPABASE_URL", "")
    SUPABASE_KEY = os.environ.get("SUPABASE_KEY", "")
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("⚠️ إعدادات Supabase مفقودة. يرجى إعداد متغيرات البيئة أو ملف .env")
        print(f"SUPABASE_URL: {'✅' if SUPABASE_URL else '❌'}")
        print(f"SUPABASE_KEY: {'✅' if SUPABASE_KEY else '❌'}")
    else:
        print(f"✅ تم تحميل إعدادات Supabase من متغيرات البيئة")

# التحقق من صحة URL
if SUPABASE_URL and not SUPABASE_URL.startswith(('http://', 'https://')):
    print(f"❌ رابط Supabase غير صحيح: {SUPABASE_URL}")
    SUPABASE_URL = ""
elif SUPABASE_URL:
    # إزالة الشرطة المائلة الزائدة في النهاية
    SUPABASE_URL = SUPABASE_URL.rstrip('/')
    print(f"✅ تم تحميل رابط Supabase: {SUPABASE_URL}")

# مفتاح الخدمة للعمليات التي تتطلب صلاحيات أعلى
try:
    import os
    SUPABASE_SERVICE_KEY = os.environ.get("SUPABASE_SERVICE_KEY", SUPABASE_KEY)
except:
    SUPABASE_SERVICE_KEY = SUPABASE_KEY

# إعداد headers للطلبات
HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
}

def test_supabase_connection():
    """اختبار الاتصال مع Supabase"""
    if not SUPABASE_URL or not SUPABASE_KEY:
        logger.error("❌ إعدادات Supabase مفقودة")
        return False

    try:
        # اختبار بسيط للاتصال
        url = f"{SUPABASE_URL}/rest/v1/"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info("✅ تم الاتصال بـ Supabase بنجاح")
            return True
        else:
            logger.error(f"❌ فشل الاتصال بـ Supabase: {response.status_code}")
            return False

    except requests.exceptions.RequestException as e:
        logger.error(f"❌ خطأ في الاتصال بـ Supabase: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع في الاتصال بـ Supabase: {e}")
        return False

def safe_supabase_request(method, url, **kwargs):
    """طلب آمن إلى Supabase مع معالجة الأخطاء"""
    if not SUPABASE_URL or not SUPABASE_KEY:
        logger.error("❌ إعدادات Supabase مفقودة")
        return None

    try:
        # إضافة timeout افتراضي
        kwargs.setdefault('timeout', 30)
        kwargs.setdefault('headers', HEADERS)

        response = requests.request(method, url, **kwargs)

        if response.status_code in [200, 201]:
            return response
        else:
            logger.warning(f"⚠️ استجابة غير متوقعة من Supabase: {response.status_code} - {response.text}")
            return response

    except requests.exceptions.Timeout:
        logger.error("❌ انتهت مهلة الاتصال مع Supabase")
        return None
    except requests.exceptions.ConnectionError:
        logger.error("❌ فشل الاتصال مع Supabase")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ خطأ في طلب Supabase: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع في طلب Supabase: {e}")
        return None

def get_all_mods() -> List[Dict]:
    """
    جلب جميع المودات من قاعدة البيانات Supabase
    Returns: قائمة بجميع المودات
    """
    try:
        logger.info("جاري جلب جميع المودات من Supabase...")

        # جلب البيانات من جدول mods
        url = f"{SUPABASE_URL}/rest/v1/mods"
        response = safe_supabase_request('GET', url)

        if response and response.status_code == 200:
            data = response.json()
            mods = []
            for row in data:
                # تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
                mod = {
                    'id': row['id'],
                    'title': row['name'] or 'Untitled Mod',
                    'description': {
                        'ar': row['description_ar'] or row['description'] or 'لا يوجد وصف',
                        'en': row['description'] or 'No description available'
                    },
                    # إضافة أوصاف التيليجرام المخصصة
                    'telegram_description_ar': row.get('telegram_description_ar'),
                    'telegram_description_en': row.get('telegram_description_en'),
                    'download_url': row['download_url'],
                    'image_urls': row.get('image_urls') if row.get('image_urls') and isinstance(row['image_urls'], list) else [],
                    'version': row['version'],
                    'category': row['category'], # New field
                    'mod_type': 'mod',  # Default
                    'for': 'minecraft' # Assuming 'for' is always minecraft based on context
                }

                mods.append(mod)

            logger.info(f"تم جلب {len(mods)} مود من قاعدة البيانات")
            return mods
        elif response:
            logger.warning(f"فشل في جلب المودات: {response.status_code} - {response.text}")
            return []
        else:
            logger.error("فشل في الاتصال بقاعدة البيانات لجلب المودات")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب المودات من Supabase: {e}")
        return []

def get_mod_by_id(mod_id) -> Optional[Dict]:
    """
    جلب مود واحد بواسطة المعرف
    Args:
        mod_id: معرف المود (UUID string أو int)
    Returns: بيانات المود أو None إذا لم يوجد
    """
    try:
        logger.info(f"جاري جلب المود بالمعرف {mod_id} من Supabase...")

        # جلب البيانات من جدول mods
        url = f"{SUPABASE_URL}/rest/v1/mods?id=eq.{mod_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                row = data[0]
                mod = {
                    'id': row['id'],
                    'title': row['name'] or 'Untitled Mod',
                    'description': {
                        'ar': row['description_ar'] or row['description'] or 'لا يوجد وصف',
                        'en': row['description'] or 'No description available'
                    },
                    # إضافة أوصاف التيليجرام المخصصة
                    'telegram_description_ar': row.get('telegram_description_ar'),
                    'telegram_description_en': row.get('telegram_description_en'),
                    'download_url': row['download_url'],
                    'download_link': row['download_url'],  # إضافة للتوافق مع web_server.py
                    'image_urls': row.get('image_urls') if row.get('image_urls') and isinstance(row['image_urls'], list) else [],
                    'version': row['version'],
                    'category': row['category'], # New field
                    'mod_type': 'mod',
                    'for': 'minecraft'
                }

                logger.info(f"تم جلب المود {mod_id} بنجاح")
                return mod
            else:
                logger.warning(f"لم يتم العثور على المود بالمعرف {mod_id}")
                return None
        else:
            logger.warning(f"فشل في جلب المود {mod_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب المود {mod_id} من Supabase: {e}")
        return None

def delete_mod_from_db(mod_id: int) -> bool:
    """
    حذف مود من قاعدة البيانات
    Args:
        mod_id: معرف المود المراد حذفه
    Returns: True إذا تم الحذف بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري حذف المود {mod_id} من Supabase...")

        url = f"{SUPABASE_URL}/rest/v1/mods?id=eq.{mod_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف المود {mod_id} بنجاح من قاعدة البيانات")
            return True
        elif response.status_code == 404:
            logger.warning(f"لم يتم العثور على المود {mod_id} للحذف")
            return False
        else:
            logger.warning(f"فشل في حذف المود {mod_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف المود {mod_id} من Supabase: {e}")
        return False

def add_mod_to_db(mod_data: Dict) -> bool:
    """
    إضافة مود جديد إلى قاعدة البيانات
    Args:
        mod_data: بيانات المود
    Returns: True إذا تم الإضافة بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري إضافة مود جديد إلى Supabase...")

        # تحضير البيانات للإدراج
        insert_data = {
            'name': mod_data.get('title', 'Untitled Mod'),
            'description': mod_data.get('description', {}).get('en', 'No description available'), # Use English description for 'description' column
            'description_ar': mod_data.get('description', {}).get('ar', 'لا يوجد وصف'), # New Arabic description column
            'image_urls': mod_data.get('image_urls') if mod_data.get('image_urls') else [], # Store as list
            'download_url': mod_data.get('download_url'),
            'version': mod_data.get('version'),
            'category': mod_data.get('category'), # New category field
            'mod_type': mod_data.get('mod_type', 'mod') # Keep mod_type if needed for internal logic, but not in DB
        }

        url = f"{SUPABASE_URL}/rest/v1/mods"
        response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم إضافة المود الجديد بنجاح")
            return True
        else:
            logger.error(f"فشل في إضافة المود الجديد: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إضافة المود إلى Supabase: {e}")
        return False

def update_mod_in_db(mod_id: int, mod_data: Dict) -> bool:
    """
    تحديث مود موجود في قاعدة البيانات
    Args:
        mod_id: معرف المود
        mod_data: البيانات الجديدة
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تحديث المود {mod_id} في Supabase...")

        # تحضير البيانات للتحديث
        update_data = {
            'name': mod_data.get('title'),
            'description': mod_data.get('description', {}).get('en'), # Use English description for 'description' column
            'description_ar': mod_data.get('description', {}).get('ar'), # New Arabic description column
            'image_urls': mod_data.get('image_urls') if mod_data.get('image_urls') else [], # Store as list
            'download_url': mod_data.get('download_url'),
            'version': mod_data.get('version'),
            'category': mod_data.get('category'), # New category field
            'mod_type': mod_data.get('mod_type') # Keep mod_type if needed for internal logic, but not in DB
        }

        # إزالة القيم الفارغة
        update_data = {k: v for k, v in update_data.items() if v is not None}

        url = f"{SUPABASE_URL}/rest/v1/mods?id=eq.{mod_id}"
        response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم تحديث المود {mod_id} بنجاح")
            return True
        elif response.status_code == 404:
            logger.warning(f"لم يتم العثور على المود {mod_id} للتحديث")
            return False
        else:
            logger.warning(f"فشل في تحديث المود {mod_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تحديث المود {mod_id} في Supabase: {e}")
        return False

def get_mods_count() -> int:
    """
    الحصول على عدد المودات في قاعدة البيانات
    Returns: عدد المودات
    """
    try:
        url = f"{SUPABASE_URL}/rest/v1/mods?select=id"
        headers_with_count = HEADERS.copy()
        headers_with_count['Prefer'] = 'count=exact'

        response = requests.head(url, headers=headers_with_count, timeout=10)

        if response.status_code == 200:
            count_header = response.headers.get('Content-Range', '0')
            # Content-Range format: "0-1/2" where 2 is the total count
            if '/' in count_header:
                return int(count_header.split('/')[-1])
            return 0
        else:
            logger.warning(f"فشل في الحصول على عدد المودات: {response.status_code}")
            return 0
    except Exception as e:
        logger.error(f"خطأ في الحصول على عدد المودات من Supabase: {e}")
        return 0

def search_mods(search_term: str) -> List[Dict]:
    """
    البحث عن المودات بالاسم أو الوصف
    Args:
        search_term: مصطلح البحث
    Returns: قائمة بالمودات المطابقة
    """
    try:
        logger.info(f"البحث عن المودات بالمصطلح: {search_term}")

        # البحث في الاسم والوصف
        url = f"{SUPABASE_URL}/rest/v1/mods?or=(name.ilike.*{search_term}*,description.ilike.*{search_term}*)"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            mods = []
            for row in data:
                mod = {
                    'id': row['id'],
                    'title': row['name'] or 'Untitled Mod',
                    'description': {
                        'ar': row['description_ar'] or row['description'] or 'لا يوجد وصف',
                        'en': row['description'] or 'No description available'
                    },
                    # إضافة أوصاف التيليجرام المخصصة
                    'telegram_description_ar': row.get('telegram_description_ar'),
                    'telegram_description_en': row.get('telegram_description_en'),
                    'download_url': row['download_url'],
                    'image_urls': row.get('image_urls') if row.get('image_urls') and isinstance(row['image_urls'], list) else [],
                    'version': row['version'],
                    'category': row['category'], # New field
                    'mod_type': 'mod',
                    'for': 'minecraft'
                }

                mods.append(mod)

            logger.info(f"تم العثور على {len(mods)} مود مطابق للبحث")
            return mods
        else:
            logger.warning(f"فشل في البحث عن المودات: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في البحث عن المودات في Supabase: {e}")
        return []

# --- دوال إدارة روابط التحميل المخصصة ---

def get_custom_download_link(channel_id: str) -> Optional[str]:
    """
    جلب رابط التحميل المخصص لقناة معينة
    Args:
        channel_id: معرف القناة
    Returns: رابط التحميل المخصص أو None إذا لم يوجد
    """
    try:
        logger.info(f"جاري جلب رابط التحميل المخصص للقناة {channel_id}")

        url = f"{SUPABASE_URL}/rest/v1/custom_download_links?channel_id=eq.{channel_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                custom_link = data[0]['custom_link']
                logger.info(f"تم العثور على رابط مخصص للقناة {channel_id}: {custom_link}")
                return custom_link
            else:
                logger.debug(f"لا يوجد رابط مخصص للقناة {channel_id}")
                return None
        else:
            logger.warning(f"فشل في جلب الرابط المخصص للقناة {channel_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب الرابط المخصص للقناة {channel_id}: {e}")
        return None

def set_custom_download_link(channel_id: str, custom_url: str, created_by: str = None) -> bool:
    """
    تعيين رابط تحميل مخصص لقناة معينة
    Args:
        channel_id: معرف القناة
        custom_url: الرابط المخصص
        created_by: معرف المستخدم الذي أنشأ الرابط (اختياري)
    Returns: True إذا تم التعيين بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تعيين رابط مخصص للقناة {channel_id}: {custom_url}")

        # التحقق من وجود رابط مخصص للقناة
        existing_link = get_custom_download_link(channel_id)

        if existing_link:
            # تحديث الرابط الموجود
            update_data = {
                'custom_link': custom_url,
                'updated_at': 'now()'
            }
            if created_by:
                update_data['created_by'] = created_by

            url = f"{SUPABASE_URL}/rest/v1/custom_download_links?channel_id=eq.{channel_id}"
            response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث الرابط المخصص للقناة {channel_id}")
                return True
            else:
                logger.error(f"فشل في تحديث الرابط المخصص للقناة {channel_id}: {response.status_code} - {response.text}")
                return False
        else:
            # إنشاء رابط جديد
            insert_data = {
                'channel_id': channel_id,
                'custom_link': custom_url,
                'created_at': 'now()',
                'updated_at': 'now()'
            }
            if created_by:
                insert_data['created_by'] = created_by

            url = f"{SUPABASE_URL}/rest/v1/custom_download_links"
            response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

            if response.status_code == 201:
                logger.info(f"تم إنشاء رابط مخصص جديد للقناة {channel_id}")
                return True
            else:
                logger.error(f"فشل في إنشاء الرابط المخصص للقناة {channel_id}: {response.status_code} - {response.text}")
                return False

    except Exception as e:
        logger.error(f"خطأ في تعيين الرابط المخصص للقناة {channel_id}: {e}")
        return False

def delete_custom_download_link(channel_id: str) -> bool:
    """
    حذف رابط التحميل المخصص لقناة معينة
    Args:
        channel_id: معرف القناة
    Returns: True إذا تم الحذف بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري حذف الرابط المخصص للقناة {channel_id}")

        url = f"{SUPABASE_URL}/rest/v1/custom_download_links?channel_id=eq.{channel_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف الرابط المخصص للقناة {channel_id}")
            return True
        elif response.status_code == 404:
            logger.warning(f"لا يوجد رابط مخصص للقناة {channel_id} للحذف")
            return False
        else:
            logger.error(f"فشل في حذف الرابط المخصص للقناة {channel_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف الرابط المخصص للقناة {channel_id}: {e}")
        return False

def get_all_custom_download_links() -> List[Dict]:
    """
    جلب جميع روابط التحميل المخصصة
    Returns: قائمة بجميع الروابط المخصصة
    """
    try:
        logger.info("جاري جلب جميع الروابط المخصصة")

        url = f"{SUPABASE_URL}/rest/v1/custom_download_links"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} رابط مخصص")
            return data
        else:
            logger.warning(f"فشل في جلب الروابط المخصصة: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب الروابط المخصصة: {e}")
        return []

# --- دوال إدارة إعدادات الإعلانات ---

def get_user_ads_settings(user_id: str) -> Optional[Dict]:
    """
    جلب إعدادات الإعلانات لمستخدم معين
    Args:
        user_id: معرف المستخدم
    Returns: إعدادات الإعلانات أو None إذا لم توجد
    """
    try:
        logger.info(f"جاري جلب إعدادات الإعلانات للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب إعدادات الإعلانات للمستخدم {user_id}")
                return data[0]  # إرجاع أول نتيجة
            else:
                logger.info(f"لا توجد إعدادات إعلانات للمستخدم {user_id}")
                return None
        else:
            logger.warning(f"فشل في جلب إعدادات الإعلانات للمستخدم {user_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب إعدادات الإعلانات للمستخدم {user_id}: {e}")
        return None

def set_user_ads_settings(user_id: str, channel_id: str, ads_settings: Dict) -> bool:
    """
    تعيين أو تحديث إعدادات الإعلانات لمستخدم
    Args:
        user_id: معرف المستخدم
        channel_id: معرف القناة
        ads_settings: إعدادات الإعلانات
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تعيين إعدادات الإعلانات للمستخدم {user_id}")

        # التحقق من وجود إعدادات سابقة
        existing_settings = get_user_ads_settings(user_id)

        if existing_settings:
            # تحديث الإعدادات الموجودة
            update_data = {
                'channel_id': channel_id,
                'updated_at': 'now()',
                **ads_settings
            }

            url = f"{SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.{user_id}"
            response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث إعدادات الإعلانات للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في تحديث إعدادات الإعلانات للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False
        else:
            # إنشاء إعدادات جديدة
            insert_data = {
                'user_id': user_id,
                'channel_id': channel_id,
                'created_at': 'now()',
                'updated_at': 'now()',
                **ads_settings
            }

            url = f"{SUPABASE_URL}/rest/v1/user_ads_settings"
            response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

            if response.status_code == 201:
                logger.info(f"تم إنشاء إعدادات إعلانات جديدة للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في إنشاء إعدادات الإعلانات للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False

    except Exception as e:
        logger.error(f"خطأ في تعيين إعدادات الإعلانات للمستخدم {user_id}: {e}")
        return False

def update_ads_stats(user_id: str, stat_type: str) -> bool:
    """
    تحديث إحصائيات الإعلانات (مشاهدات أو نقرات)
    Args:
        user_id: معرف المستخدم
        stat_type: نوع الإحصائية ('views' أو 'clicks')
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تحديث إحصائية {stat_type} للمستخدم {user_id}")

        # تحديد العمود المناسب
        column_name = 'total_ad_views' if stat_type == 'views' else 'total_ad_clicks'

        # استخدام SQL لزيادة القيمة
        update_data = {
            column_name: f"COALESCE({column_name}, 0) + 1",
            'updated_at': 'now()'
        }

        url = f"{SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.{user_id}"
        response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم تحديث إحصائية {stat_type} للمستخدم {user_id}")
            return True
        else:
            logger.warning(f"فشل في تحديث إحصائية {stat_type} للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تحديث إحصائية {stat_type} للمستخدم {user_id}: {e}")
        return False

def delete_user_ads_settings(user_id: str) -> bool:
    """
    حذف إعدادات الإعلانات لمستخدم
    Args:
        user_id: معرف المستخدم
    Returns: True إذا تم الحذف بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري حذف إعدادات الإعلانات للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 204:
            logger.info(f"تم حذف إعدادات الإعلانات للمستخدم {user_id}")
            return True
        else:
            logger.warning(f"فشل في حذف إعدادات الإعلانات للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف إعدادات الإعلانات للمستخدم {user_id}: {e}")
        return False

# --- دوال نظام المهام ---

def get_user_tasks_settings(user_id: str) -> Optional[Dict]:
    """
    جلب إعدادات نظام المهام لمستخدم معين
    Args:
        user_id: معرف المستخدم
    Returns: إعدادات نظام المهام أو None إذا لم توجد
    """
    try:
        logger.info(f"جاري جلب إعدادات نظام المهام للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_tasks_settings?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب إعدادات نظام المهام للمستخدم {user_id}")
                return data[0]
            else:
                logger.info(f"لا توجد إعدادات نظام المهام للمستخدم {user_id}")
                return None
        else:
            logger.warning(f"فشل في جلب إعدادات نظام المهام للمستخدم {user_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب إعدادات نظام المهام للمستخدم {user_id}: {e}")
        return None

def set_user_tasks_settings(user_id: str, channel_id: str, tasks_settings: Dict) -> bool:
    """
    تعيين أو تحديث إعدادات نظام المهام لمستخدم
    Args:
        user_id: معرف المستخدم
        channel_id: معرف القناة
        tasks_settings: إعدادات نظام المهام
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تعيين إعدادات نظام المهام للمستخدم {user_id}")

        # التحقق من وجود إعدادات سابقة
        existing_settings = get_user_tasks_settings(user_id)

        if existing_settings:
            # تحديث الإعدادات الموجودة
            update_data = {
                'channel_id': channel_id,
                'updated_at': 'now()',
                **tasks_settings
            }

            url = f"{SUPABASE_URL}/rest/v1/user_tasks_settings?user_id=eq.{user_id}"
            response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث إعدادات نظام المهام للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في تحديث إعدادات نظام المهام للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False
        else:
            # إنشاء إعدادات جديدة
            insert_data = {
                'user_id': user_id,
                'channel_id': channel_id,
                'created_at': 'now()',
                'updated_at': 'now()',
                **tasks_settings
            }

            url = f"{SUPABASE_URL}/rest/v1/user_tasks_settings"
            response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

            if response.status_code == 201:
                logger.info(f"تم إنشاء إعدادات نظام المهام جديدة للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في إنشاء إعدادات نظام المهام للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False

    except Exception as e:
        logger.error(f"خطأ في تعيين إعدادات نظام المهام للمستخدم {user_id}: {e}")
        return False

def get_available_tasks(channel_owner_id: str, active_only: bool = True) -> List[Dict]:
    """
    جلب المهام المتاحة لقناة معينة
    Args:
        channel_owner_id: معرف صاحب القناة
        active_only: جلب المهام النشطة فقط
    Returns: قائمة المهام
    """
    try:
        logger.info(f"جاري جلب المهام المتاحة لصاحب القناة {channel_owner_id}")

        url = f"{SUPABASE_URL}/rest/v1/tasks?created_by=eq.{channel_owner_id}"
        if active_only:
            url += "&is_active=eq.true"
        url += "&order=created_at.asc"

        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} مهمة لصاحب القناة {channel_owner_id}")
            return data
        else:
            logger.warning(f"فشل في جلب المهام لصاحب القناة {channel_owner_id}: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب المهام لصاحب القناة {channel_owner_id}: {e}")
        return []

def add_task(user_id: str, channel_id: str, task_data: Dict) -> bool:
    """
    إضافة مهمة جديدة
    Args:
        user_id: معرف صاحب القناة
        channel_id: معرف القناة
        task_data: بيانات المهمة
    Returns: True إذا تم الإضافة بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري إضافة مهمة جديدة للمستخدم {user_id}")

        insert_data = {
            'created_by': user_id,
            'created_at': 'now()',
            'updated_at': 'now()',
            **task_data
        }

        url = f"{SUPABASE_URL}/rest/v1/tasks"
        response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم إضافة مهمة جديدة للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في إضافة مهمة للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إضافة مهمة للمستخدم {user_id}: {e}")
        return False

def update_task(task_id: int, task_data: Dict) -> bool:
    """
    تحديث مهمة موجودة
    Args:
        task_id: معرف المهمة
        task_data: البيانات الجديدة للمهمة
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تحديث المهمة {task_id}")

        update_data = {
            'updated_at': 'now()',
            **task_data
        }

        url = f"{SUPABASE_URL}/rest/v1/tasks?id=eq.{task_id}"
        response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم تحديث المهمة {task_id}")
            return True
        else:
            logger.error(f"فشل في تحديث المهمة {task_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تحديث المهمة {task_id}: {e}")
        return False

def delete_task(task_id: int) -> bool:
    """
    حذف مهمة
    Args:
        task_id: معرف المهمة
    Returns: True إذا تم الحذف بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري حذف المهمة {task_id}")

        url = f"{SUPABASE_URL}/rest/v1/tasks?id=eq.{task_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 204:
            logger.info(f"تم حذف المهمة {task_id}")
            return True
        else:
            logger.error(f"فشل في حذف المهمة {task_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف المهمة {task_id}: {e}")
        return False

def get_user_task_completions(user_id: str, channel_owner_id: str) -> List[Dict]:
    """
    جلب المهام المكتملة لمستخدم معين من قناة معينة
    Args:
        user_id: معرف المستخدم
        channel_owner_id: معرف صاحب القناة
    Returns: قائمة المهام المكتملة
    """
    try:
        logger.info(f"جاري جلب المهام المكتملة للمستخدم {user_id} من القناة {channel_owner_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_task_completions?user_id=eq.{user_id}&select=*,tasks(*)"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} مهمة مكتملة للمستخدم {user_id}")
            return data
        else:
            logger.warning(f"فشل في جلب المهام المكتملة للمستخدم {user_id}: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب المهام المكتملة للمستخدم {user_id}: {e}")
        return []

def mark_task_completed(user_id: str, task_id: int, channel_owner_id: str, verification_data: str = None) -> bool:
    """
    تسجيل إكمال مهمة من قبل مستخدم
    Args:
        user_id: معرف المستخدم
        task_id: معرف المهمة
        channel_owner_id: معرف صاحب القناة
        verification_data: بيانات التحقق (اختياري)
    Returns: True إذا تم التسجيل بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تسجيل إكمال المهمة {task_id} للمستخدم {user_id}")

        insert_data = {
            'user_id': user_id,
            'task_id': task_id,
            'completed_at': 'now()',
            'verified': True
        }

        url = f"{SUPABASE_URL}/rest/v1/user_task_completions"
        response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم تسجيل إكمال المهمة {task_id} للمستخدم {user_id}")

            # تحديث عداد إكمال المهمة
            update_url = f"{SUPABASE_URL}/rest/v1/tasks?id=eq.{task_id}"
            update_data = {'current_completions': 'current_completions + 1'}
            requests.patch(update_url, headers=HEADERS, json=update_data, timeout=10)

            return True
        else:
            logger.error(f"فشل في تسجيل إكمال المهمة {task_id} للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تسجيل إكمال المهمة {task_id} للمستخدم {user_id}: {e}")
        return False

def check_user_completed_all_tasks(user_id: str, channel_owner_id: str) -> bool:
    """
    التحقق من إكمال المستخدم لجميع المهام المطلوبة
    Args:
        user_id: معرف المستخدم
        channel_owner_id: معرف صاحب القناة
    Returns: True إذا أكمل جميع المهام، False إذا لم يكمل
    """
    try:
        logger.info(f"جاري التحقق من إكمال جميع المهام للمستخدم {user_id} من القناة {channel_owner_id}")

        # استخدام الدالة المخزنة في قاعدة البيانات
        url = f"{SUPABASE_URL}/rpc/check_all_required_tasks_completed"
        data = {
            'p_user_id': user_id,
            'p_channel_owner_id': channel_owner_id
        }

        response = requests.post(url, headers=HEADERS, json=data, timeout=10)

        if response.status_code == 200:
            result = response.json()
            logger.info(f"نتيجة التحقق من إكمال المهام للمستخدم {user_id}: {result}")
            return result
        else:
            logger.warning(f"فشل في التحقق من إكمال المهام للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في التحقق من إكمال المهام للمستخدم {user_id}: {e}")
        return False

def add_completed_user(user_id: str, channel_owner_id: str, channel_id: str) -> bool:
    """
    إضافة مستخدم إلى قائمة المكتملين
    Args:
        user_id: معرف المستخدم
        channel_owner_id: معرف صاحب القناة
        channel_id: معرف القناة
    Returns: True إذا تم الإضافة بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري إضافة المستخدم {user_id} إلى قائمة المكتملين للقناة {channel_owner_id}")

        # استخدام الدالة المخزنة في قاعدة البيانات
        url = f"{SUPABASE_URL}/rpc/add_completed_user"
        data = {
            'p_user_id': user_id,
            'p_channel_owner_id': channel_owner_id,
            'p_channel_id': channel_id
        }

        response = requests.post(url, headers=HEADERS, json=data, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم إضافة المستخدم {user_id} إلى قائمة المكتملين")

            # تحديث إحصائيات نظام المهام
            update_stats_url = f"{SUPABASE_URL}/rest/v1/user_tasks_settings?user_id=eq.{channel_owner_id}"
            update_data = {'total_task_completions': 'total_task_completions + 1'}
            requests.patch(update_stats_url, headers=HEADERS, json=update_data, timeout=10)

            return True
        else:
            logger.error(f"فشل في إضافة المستخدم {user_id} إلى قائمة المكتملين: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إضافة المستخدم {user_id} إلى قائمة المكتملين: {e}")
        return False

def is_user_completed(user_id: str, channel_owner_id: str) -> bool:
    """
    التحقق من وجود المستخدم في قائمة المكتملين
    Args:
        user_id: معرف المستخدم
        channel_owner_id: معرف صاحب القناة
    Returns: True إذا كان في القائمة، False إذا لم يكن
    """
    try:
        logger.info(f"جاري التحقق من وجود المستخدم {user_id} في قائمة المكتملين للقناة {channel_owner_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_task_completions?user_id=eq.{user_id}&select=*"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            result = len(data) > 0
            logger.info(f"المستخدم {user_id} {'موجود' if result else 'غير موجود'} في قائمة المكتملين")
            return result
        else:
            logger.warning(f"فشل في التحقق من وجود المستخدم {user_id} في قائمة المكتملين: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في التحقق من وجود المستخدم {user_id} في قائمة المكتملين: {e}")
        return False

def get_tasks_stats(channel_owner_id: str) -> Dict:
    """
    جلب إحصائيات نظام المهام
    Args:
        channel_owner_id: معرف صاحب القناة
    Returns: إحصائيات نظام المهام
    """
    try:
        logger.info(f"جاري جلب إحصائيات نظام المهام للقناة {channel_owner_id}")

        # إحصائيات المهام
        tasks_url = f"{SUPABASE_URL}/rest/v1/tasks?created_by=eq.{channel_owner_id}&select=count"
        tasks_response = requests.get(tasks_url, headers=HEADERS, timeout=10)

        # إحصائيات الإكمالات
        completions_url = f"{SUPABASE_URL}/rest/v1/user_task_completions?select=count"
        completions_response = requests.get(completions_url, headers=HEADERS, timeout=10)

        stats = {
            'total_tasks': 0,
            'total_task_completions': 0
        }

        if tasks_response.status_code == 200:
            stats['total_tasks'] = len(tasks_response.json())

        if completions_response.status_code == 200:
            stats['total_task_completions'] = len(completions_response.json())

        logger.info(f"تم جلب إحصائيات نظام المهام للقناة {channel_owner_id}")
        return stats

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات نظام المهام للقناة {channel_owner_id}: {e}")
        return {'total_tasks': 0, 'total_task_completions': 0}

# --- دوال إدارة إعدادات اختصار الروابط ---

def get_user_url_shortener_settings(user_id: str) -> dict:
    """
    جلب إعدادات اختصار الروابط للمستخدم
    Args:
        user_id: معرف المستخدم
    Returns:
        dict: إعدادات اختصار الروابط أو None إذا لم توجد
    """
    try:
        logger.info(f"جاري جلب إعدادات اختصار الروابط للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_url_shortener_settings?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب إعدادات اختصار الروابط للمستخدم {user_id}")
                return data[0]  # إرجاع أول نتيجة
            else:
                logger.info(f"لا توجد إعدادات اختصار روابط للمستخدم {user_id}")
                return None
        else:
            logger.warning(f"فشل في جلب إعدادات اختصار الروابط للمستخدم {user_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"Error getting user URL shortener settings for user {user_id}: {e}")
        return None

def set_user_url_shortener_settings(user_id: str, channel_id: str, settings: dict) -> bool:
    """
    حفظ أو تحديث إعدادات اختصار الروابط للمستخدم
    Args:
        user_id: معرف المستخدم
        channel_id: معرف القناة
        settings: إعدادات اختصار الروابط
    Returns:
        bool: True إذا نجحت العملية، False إذا فشلت
    """
    try:
        logger.info(f"جاري تعيين إعدادات اختصار الروابط للمستخدم {user_id}")

        # التحقق من وجود إعدادات سابقة
        existing = get_user_url_shortener_settings(user_id)

        # تحضير البيانات
        data = {
            "user_id": user_id,
            "channel_id": channel_id,
            "shortener_enabled": settings.get("shortener_enabled", False),
            "api_url": settings.get("api_url", ""),
            "api_key": settings.get("api_key", ""),
            "service_name": settings.get("service_name", "custom"),
            "use_custom_alias": settings.get("use_custom_alias", True),
            "alias_prefix": settings.get("alias_prefix", "mod")
        }

        if existing:
            # تحديث الإعدادات الموجودة
            update_data = data.copy()
            update_data['updated_at'] = 'now()'

            url = f"{SUPABASE_URL}/rest/v1/user_url_shortener_settings?user_id=eq.{user_id}"
            response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث إعدادات اختصار الروابط للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في تحديث إعدادات اختصار الروابط للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False
        else:
            # إنشاء إعدادات جديدة
            insert_data = data.copy()
            insert_data['created_at'] = 'now()'
            insert_data['updated_at'] = 'now()'

            url = f"{SUPABASE_URL}/rest/v1/user_url_shortener_settings"
            response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

            if response.status_code == 201:
                logger.info(f"تم إنشاء إعدادات اختصار الروابط للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في إنشاء إعدادات اختصار الروابط للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False

    except Exception as e:
        logger.error(f"Error setting user URL shortener settings for user {user_id}: {e}")
        return False

def delete_user_url_shortener_settings(user_id: str) -> bool:
    """
    حذف إعدادات اختصار الروابط للمستخدم
    Args:
        user_id: معرف المستخدم
    Returns:
        bool: True إذا نجحت العملية، False إذا فشلت
    """
    try:
        logger.info(f"جاري حذف إعدادات اختصار الروابط للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_url_shortener_settings?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف إعدادات اختصار الروابط للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.warning(f"لا توجد إعدادات اختصار روابط للمستخدم {user_id} للحذف")
            return False
        else:
            logger.error(f"فشل في حذف إعدادات اختصار الروابط للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"Error deleting user URL shortener settings for user {user_id}: {e}")
        return False

def update_url_shortener_stats(user_id: str, urls_shortened: int = 0, clicks: int = 0) -> bool:
    """
    تحديث إحصائيات اختصار الروابط للمستخدم
    Args:
        user_id: معرف المستخدم
        urls_shortened: عدد الروابط المختصرة الجديدة
        clicks: عدد النقرات الجديدة
    Returns:
        bool: True إذا نجحت العملية، False إذا فشلت
    """
    try:
        logger.info(f"جاري تحديث إحصائيات اختصار الروابط للمستخدم {user_id}")

        # جلب الإحصائيات الحالية
        current_settings = get_user_url_shortener_settings(user_id)
        if not current_settings:
            logger.warning(f"لا توجد إعدادات اختصار روابط للمستخدم {user_id}")
            return False

        # تحديث الإحصائيات
        new_total_urls = current_settings.get("total_urls_shortened", 0) + urls_shortened
        new_total_clicks = current_settings.get("total_clicks", 0) + clicks

        update_data = {
            "total_urls_shortened": new_total_urls,
            "total_clicks": new_total_clicks,
            "updated_at": "now()"
        }

        # إضافة تاريخ آخر اختصار إذا تم اختصار روابط جديدة
        if urls_shortened > 0:
            from datetime import datetime, timezone
            update_data["last_shortened_at"] = datetime.now(timezone.utc).isoformat()

        url = f"{SUPABASE_URL}/rest/v1/user_url_shortener_settings?user_id=eq.{user_id}"
        response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم تحديث إحصائيات اختصار الروابط للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في تحديث إحصائيات اختصار الروابط للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"Error updating URL shortener stats for user {user_id}: {e}")
        return False

def get_url_shortener_stats() -> dict:
    """
    جلب إحصائيات عامة لاختصار الروابط
    Returns:
        dict: إحصائيات عامة
    """
    try:
        logger.info("جاري جلب إحصائيات اختصار الروابط العامة")

        # جلب جميع المستخدمين الذين لديهم إعدادات اختصار الروابط
        url = f"{SUPABASE_URL}/rest/v1/user_url_shortener_settings"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()

            if not data:
                return {
                    "total_users": 0,
                    "active_users": 0,
                    "total_urls_shortened": 0,
                    "total_clicks": 0,
                    "most_used_service": "N/A"
                }

            total_users = len(data)
            active_users = len([user for user in data if user.get("shortener_enabled", False)])
            total_urls_shortened = sum(user.get("total_urls_shortened", 0) for user in data)
            total_clicks = sum(user.get("total_clicks", 0) for user in data)

            # حساب الخدمة الأكثر استخداماً
            services = [user.get("service_name", "custom") for user in data if user.get("shortener_enabled", False)]
            most_used_service = max(set(services), key=services.count) if services else "N/A"

            logger.info(f"تم جلب إحصائيات اختصار الروابط: {total_users} مستخدم، {active_users} نشط")

            return {
                "total_users": total_users,
                "active_users": active_users,
                "total_urls_shortened": total_urls_shortened,
                "total_clicks": total_clicks,
                "most_used_service": most_used_service
            }
        else:
            logger.warning(f"فشل في جلب إحصائيات اختصار الروابط: {response.status_code} - {response.text}")
            return {
                "total_users": 0,
                "active_users": 0,
                "total_urls_shortened": 0,
                "total_clicks": 0,
                "most_used_service": "N/A"
            }

    except Exception as e:
        logger.error(f"Error getting URL shortener stats: {e}")
        return {
            "total_users": 0,
            "active_users": 0,
            "total_urls_shortened": 0,
            "total_clicks": 0,
            "most_used_service": "N/A"
        }

# دوال إدارة إعدادات تخصيص صفحة المود
def get_user_page_customization_settings(user_id: str) -> dict:
    """
    جلب إعدادات تخصيص صفحة المود للمستخدم
    Args:
        user_id: معرف المستخدم
    Returns:
        dict: إعدادات التخصيص أو None إذا لم توجد
    """
    try:
        logger.info(f"جاري جلب إعدادات تخصيص الصفحة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_page_customization?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب إعدادات تخصيص الصفحة للمستخدم {user_id}")
                return data[0]  # إرجاع أول نتيجة
            else:
                logger.info(f"لا توجد إعدادات تخصيص صفحة للمستخدم {user_id}")
                return None
        else:
            logger.warning(f"فشل في جلب إعدادات تخصيص الصفحة للمستخدم {user_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"Error getting page customization settings for user {user_id}: {e}")
        return None

def set_user_page_customization_settings(user_id: str, settings: dict) -> bool:
    """
    تعيين إعدادات تخصيص صفحة المود للمستخدم
    Args:
        user_id: معرف المستخدم
        settings: إعدادات التخصيص
    Returns:
        bool: True إذا نجحت العملية، False إذا فشلت
    """
    try:
        logger.info(f"جاري تعيين إعدادات تخصيص الصفحة للمستخدم {user_id}")

        # التحقق من وجود إعدادات سابقة
        existing = get_user_page_customization_settings(user_id)

        # تحضير البيانات
        data = {
            "user_id": user_id,
            "site_name": settings.get("site_name", "Modetaris"),
            "show_all_images": settings.get("show_all_images", True),
            "download_button_text_ar": settings.get("download_button_text_ar", "تحميل المود"),
            "download_button_text_en": settings.get("download_button_text_en", "Download Mod"),
            "open_button_text_ar": settings.get("open_button_text_ar", "فتح المود"),
            "open_button_text_en": settings.get("open_button_text_en", "Open Mod"),
            "version_label_ar": settings.get("version_label_ar", "الإصدار"),
            "version_label_en": settings.get("version_label_en", "Version"),
            "category_label_ar": settings.get("category_label_ar", "تصنيف المود"),
            "category_label_en": settings.get("category_label_en", "Mod Category"),
            "description_label_ar": settings.get("description_label_ar", "الوصف"),
            "description_label_en": settings.get("description_label_en", "Description"),
            "enable_mod_opening": settings.get("enable_mod_opening", True),
            "updated_at": "now()"
        }

        if existing:
            # تحديث الإعدادات الموجودة
            url = f"{SUPABASE_URL}/rest/v1/user_page_customization?user_id=eq.{user_id}"
            response = requests.patch(url, headers=HEADERS, json=data, timeout=10)
        else:
            # إنشاء إعدادات جديدة
            data["created_at"] = "now()"
            url = f"{SUPABASE_URL}/rest/v1/user_page_customization"
            response = requests.post(url, headers=HEADERS, json=data, timeout=10)

        if response.status_code in [200, 201]:
            logger.info(f"تم تعيين إعدادات تخصيص الصفحة للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في تعيين إعدادات تخصيص الصفحة للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"Error setting page customization settings for user {user_id}: {e}")
        return False

def delete_user_page_customization_settings(user_id: str) -> bool:
    """
    حذف إعدادات تخصيص صفحة المود للمستخدم
    Args:
        user_id: معرف المستخدم
    Returns:
        bool: True إذا نجحت العملية، False إذا فشلت
    """
    try:
        logger.info(f"جاري حذف إعدادات تخصيص الصفحة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_page_customization?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف إعدادات تخصيص الصفحة للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حذف إعدادات تخصيص الصفحة للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"Error deleting page customization settings for user {user_id}: {e}")
        return False

def get_page_customization_stats() -> dict:
    """
    جلب إحصائيات استخدام تخصيص الصفحات
    Returns:
        dict: إحصائيات التخصيص
    """
    try:
        logger.info("جاري جلب إحصائيات تخصيص الصفحات")

        url = f"{SUPABASE_URL}/rest/v1/page_customization_settings"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()

            if not data:
                return {
                    "total_customized_users": 0,
                    "users_with_custom_site_name": 0,
                    "users_with_custom_buttons": 0,
                    "users_with_disabled_opening": 0,
                    "users_with_custom_styles": 0,
                    "most_popular_site_name": "Modetaris",
                    "most_popular_style": "default",
                    "style_distribution": {}
                }

            total_users = len(data)
            users_with_custom_site_name = len([user for user in data if user.get("site_name", "Modetaris") != "Modetaris"])
            users_with_custom_buttons = len([user for user in data if
                user.get("download_button_text_ar", "تحميل") != "تحميل" or
                user.get("download_button_text_en", "Download") != "Download"])
            users_with_disabled_opening = len([user for user in data if not user.get("enable_mod_opening", True)])
            users_with_custom_styles = len([user for user in data if user.get("style_template", "default") != "default"])

            # حساب اسم الموقع الأكثر شيوعاً
            site_names = [user.get("site_name", "Modetaris") for user in data]
            most_popular_site_name = max(set(site_names), key=site_names.count) if site_names else "Modetaris"

            # حساب الستايل الأكثر شيوعاً وتوزيع الأساليب
            style_templates = [user.get("style_template", "default") for user in data]
            most_popular_style = max(set(style_templates), key=style_templates.count) if style_templates else "default"

            # إحصائيات توزيع الأساليب
            from collections import Counter
            style_distribution = dict(Counter(style_templates))

            logger.info(f"تم جلب إحصائيات تخصيص الصفحات: {total_users} مستخدم مخصص")

            return {
                "total_customized_users": total_users,
                "users_with_custom_site_name": users_with_custom_site_name,
                "users_with_custom_buttons": users_with_custom_buttons,
                "users_with_disabled_opening": users_with_disabled_opening,
                "users_with_custom_styles": users_with_custom_styles,
                "most_popular_site_name": most_popular_site_name,
                "most_popular_style": most_popular_style,
                "style_distribution": style_distribution
            }
        else:
            logger.warning(f"فشل في جلب إحصائيات تخصيص الصفحات: {response.status_code} - {response.text}")
            return {
                "total_customized_users": 0,
                "users_with_custom_site_name": 0,
                "users_with_custom_buttons": 0,
                "users_with_disabled_opening": 0,
                "users_with_custom_styles": 0,
                "most_popular_site_name": "Modetaris",
                "most_popular_style": "default",
                "style_distribution": {}
            }

    except Exception as e:
        logger.error(f"Error getting page customization stats: {e}")
        return {
            "total_customized_users": 0,
            "users_with_custom_site_name": 0,
            "users_with_custom_buttons": 0,
            "users_with_disabled_opening": 0,
            "users_with_custom_styles": 0,
            "most_popular_site_name": "Modetaris",
            "most_popular_style": "default",
            "style_distribution": {}
        }

# --- دوال إدارة تخصيص صفحة المود للمستخدمين ---

def get_user_page_customization_settings(user_id: str) -> Optional[Dict]:
    """
    جلب إعدادات تخصيص الصفحة لمستخدم معين
    Args:
        user_id: معرف المستخدم
    Returns: إعدادات التخصيص أو None إذا لم توجد
    """
    try:
        logger.info(f"جاري جلب إعدادات تخصيص الصفحة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب إعدادات تخصيص الصفحة للمستخدم {user_id}")
                return data[0]  # إرجاع أول نتيجة
            else:
                logger.info(f"لا توجد إعدادات تخصيص للمستخدم {user_id}")
                return None
        else:
            logger.warning(f"فشل في جلب إعدادات التخصيص للمستخدم {user_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب إعدادات التخصيص للمستخدم {user_id}: {e}")
        return None

def delete_old_channel_logo(user_id: str) -> bool:
    """
    حذف صورة القناة القديمة للمستخدم
    Args:
        user_id: معرف المستخدم
    Returns: True إذا تم الحذف بنجاح أو لم توجد صورة قديمة
    """
    try:
        # جلب الإعدادات الحالية
        current_settings = get_user_page_customization_settings(user_id)

        if not current_settings or not current_settings.get('channel_logo_url'):
            logger.info(f"لا توجد صورة قديمة للحذف للمستخدم {user_id}")
            return True

        old_logo_url = current_settings['channel_logo_url']
        logger.info(f"جاري حذف الصورة القديمة للمستخدم {user_id}: {old_logo_url}")

        # إذا كانت الصورة محفوظة محلياً في htdocs
        if '1c547fe5.sendaddons.pages.dev/uploaded_images/' in old_logo_url or 'sendaddons.fwh.is/uploaded_images/' in old_logo_url:
            try:
                import os
                filename = old_logo_url.split('/')[-1]
                file_path = os.path.join("htdocs", "uploaded_images", filename)

                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"تم حذف الصورة المحلية: {file_path}")
                else:
                    logger.warning(f"الصورة المحلية غير موجودة: {file_path}")
            except Exception as e:
                logger.error(f"خطأ في حذف الصورة المحلية: {e}")

        # إذا كانت الصورة في Supabase Storage
        elif 'supabase.co/storage/' in old_logo_url:
            try:
                # استخراج اسم الملف من الرابط
                filename = old_logo_url.split('/')[-1]

                # حذف من Supabase Storage
                delete_url = f"{SUPABASE_URL}/storage/v1/object/avatars/{filename}"
                headers = {
                    'apikey': SUPABASE_KEY,
                    'Authorization': f'Bearer {SUPABASE_KEY}'
                }

                response = requests.delete(delete_url, headers=headers, timeout=10)

                if response.status_code in [200, 204]:
                    logger.info(f"تم حذف الصورة من Supabase Storage: {filename}")
                else:
                    logger.warning(f"فشل في حذف الصورة من Supabase Storage: {response.status_code}")

            except Exception as e:
                logger.error(f"خطأ في حذف الصورة من Supabase Storage: {e}")

        return True

    except Exception as e:
        logger.error(f"خطأ في حذف الصورة القديمة للمستخدم {user_id}: {e}")
        return False

def save_user_customization_locally(user_id: str, customization_data: Dict) -> bool:
    """حفظ إعدادات التخصيص محلياً كحل بديل"""
    try:
        import json
        import os

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        data_dir = "user_customizations"
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # ملف البيانات
        file_path = os.path.join(data_dir, f"user_{user_id}.json")

        # قراءة البيانات الموجودة أو إنشاء بيانات جديدة
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        else:
            existing_data = {
                'user_id': user_id,
                'created_at': datetime.now().isoformat()
            }

        # تحديث البيانات
        existing_data.update(customization_data)
        existing_data['updated_at'] = datetime.now().isoformat()

        # حفظ البيانات
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)

        logger.info(f"تم حفظ إعدادات التخصيص محلياً للمستخدم {user_id}")
        return True

    except Exception as e:
        logger.error(f"خطأ في حفظ إعدادات التخصيص محلياً للمستخدم {user_id}: {e}")
        return False

def update_user_page_customization(user_id: str, customization_data: Dict) -> bool:
    """
    تحديث أو إنشاء إعدادات تخصيص الصفحة لمستخدم
    Args:
        user_id: معرف المستخدم
        customization_data: بيانات التخصيص
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تحديث إعدادات تخصيص الصفحة للمستخدم {user_id}")

        # محاولة إنشاء الجدول إذا لم يكن موجوداً
        table_exists = create_page_customization_table_direct()

        if not table_exists:
            logger.warning("الجدول غير موجود، سيتم الحفظ محلياً")
            return save_user_customization_locally(user_id, customization_data)

        # التحقق من وجود إعدادات سابقة
        existing_settings = get_user_page_customization_settings(user_id)

        if existing_settings:
            # تحديث الإعدادات الموجودة في جدول page_customization_settings
            update_data = {
                'updated_at': 'now()',
                **customization_data
            }

            url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.{user_id}"
            response = requests.patch(url, headers=HEADERS, json=update_data, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث إعدادات التخصيص للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في تحديث إعدادات التخصيص للمستخدم {user_id}: {response.status_code} - {response.text}")
                # حفظ محلي كحل بديل
                return save_user_customization_locally(user_id, customization_data)
        else:
            # إنشاء إعدادات جديدة في جدول page_customization_settings
            insert_data = {
                'user_id': user_id,
                'created_at': 'now()',
                'updated_at': 'now()',
                **customization_data
            }

            url = f"{SUPABASE_URL}/rest/v1/page_customization_settings"
            response = requests.post(url, headers=HEADERS, json=insert_data, timeout=10)

            if response.status_code == 201:
                logger.info(f"تم إنشاء إعدادات تخصيص جديدة للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في إنشاء إعدادات التخصيص للمستخدم {user_id}: {response.status_code} - {response.text}")
                # حفظ محلي كحل بديل
                return save_user_customization_locally(user_id, customization_data)

    except Exception as e:
        logger.error(f"خطأ في تحديث إعدادات التخصيص للمستخدم {user_id}: {e}")
        # حفظ محلي كحل بديل
        return save_user_customization_locally(user_id, customization_data)

def delete_user_page_customization(user_id: str) -> bool:
    """
    حذف إعدادات تخصيص الصفحة لمستخدم معين
    Args:
        user_id: معرف المستخدم
    Returns: True إذا تم الحذف بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري حذف إعدادات تخصيص الصفحة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_page_customization?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف إعدادات التخصيص للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.warning(f"لا توجد إعدادات تخصيص للمستخدم {user_id} للحذف")
            return False
        else:
            logger.error(f"فشل في حذف إعدادات التخصيص للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف إعدادات التخصيص للمستخدم {user_id}: {e}")
        return False

def get_all_user_page_customizations(limit: int = 50, offset: int = 0) -> List[Dict]:
    """
    جلب جميع إعدادات تخصيص الصفحة للمستخدمين مع التصفح
    Args:
        limit: عدد النتائج المطلوبة
        offset: نقطة البداية
    Returns: قائمة بإعدادات التخصيص
    """
    try:
        logger.info(f"جاري جلب إعدادات تخصيص الصفحة (limit={limit}, offset={offset})")

        url = f"{SUPABASE_URL}/rest/v1/user_page_customization?limit={limit}&offset={offset}&order=created_at.desc"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} إعداد تخصيص")
            return data
        else:
            logger.warning(f"فشل في جلب إعدادات التخصيص: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب إعدادات التخصيص: {e}")
        return []

def create_user_page_customization_table():
    """
    إنشاء جدول تخصيص صفحة المود للمستخدمين مع جميع الحقول المطلوبة
    """
    try:
        logger.info("جاري إنشاء جدول user_page_customization...")

        # SQL لإنشاء الجدول
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS user_page_customization (
            id SERIAL PRIMARY KEY,
            user_id TEXT NOT NULL UNIQUE,
            site_name TEXT DEFAULT 'Modetaris',
            channel_logo_url TEXT,
            logo_position TEXT DEFAULT 'right' CHECK (logo_position IN ('left', 'right')),
            page_theme TEXT DEFAULT 'default' CHECK (page_theme IN ('default', 'telegram', 'dark', 'light', 'custom')),
            custom_bg_color TEXT,
            custom_header_color TEXT,
            custom_text_color TEXT,
            custom_button_color TEXT,
            custom_border_color TEXT,
            show_all_images BOOLEAN DEFAULT true,
            enable_mod_opening BOOLEAN DEFAULT true,
            download_button_text_ar TEXT DEFAULT 'تحميل',
            download_button_text_en TEXT DEFAULT 'Download',
            open_button_text_ar TEXT DEFAULT 'فتح',
            open_button_text_en TEXT DEFAULT 'Open',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- إنشاء فهرس على user_id لتحسين الأداء
        CREATE INDEX IF NOT EXISTS idx_user_page_customization_user_id ON user_page_customization(user_id);
        """

        # تنفيذ SQL باستخدام REST API
        url = f"{SUPABASE_URL}/rest/v1/rpc/execute_sql"
        payload = {"sql_query": create_table_sql}

        response = requests.post(url, headers=HEADERS, json=payload, timeout=30)

        if response.status_code in [200, 201]:
            logger.info("تم إنشاء جدول user_page_customization بنجاح")
            return True
        else:
            logger.error(f"فشل في إنشاء جدول user_page_customization: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إنشاء جدول user_page_customization: {e}")
        return False

# --- دوال إدارة نظام الإشعارات ---

def create_notifications_tables():
    """
    إنشاء جداول نظام الإشعارات
    """
    try:
        logger.info("جاري إنشاء جداول نظام الإشعارات...")

        # التحقق من الاتصال أولاً
        if not test_supabase_connection():
            logger.error("❌ فشل في الاتصال بـ Supabase")
            return False

        # SQL لإنشاء جدول الإشعارات المحفوظة
        create_notifications_sql = """
        -- جدول الإشعارات المحفوظة
        CREATE TABLE IF NOT EXISTS saved_notifications (
            id SERIAL PRIMARY KEY,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
            media_url TEXT,
            button_text TEXT,
            button_url TEXT,
            created_by TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_active BOOLEAN DEFAULT true
        );

        -- جدول سجل الإرسال
        CREATE TABLE IF NOT EXISTS notification_broadcasts (
            id SERIAL PRIMARY KEY,
            notification_id INTEGER REFERENCES saved_notifications(id) ON DELETE CASCADE,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            message_type TEXT DEFAULT 'text',
            media_url TEXT,
            button_text TEXT,
            button_url TEXT,
            sent_by TEXT NOT NULL,
            total_users INTEGER DEFAULT 0,
            successful_sends INTEGER DEFAULT 0,
            failed_sends INTEGER DEFAULT 0,
            started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completed_at TIMESTAMP WITH TIME ZONE,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
            error_message TEXT
        );

        -- جدول تفاصيل الإرسال لكل مستخدم
        CREATE TABLE IF NOT EXISTS notification_user_logs (
            id SERIAL PRIMARY KEY,
            broadcast_id INTEGER REFERENCES notification_broadcasts(id) ON DELETE CASCADE,
            user_id TEXT NOT NULL,
            sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
            error_message TEXT
        );

        -- إنشاء فهارس لتحسين الأداء
        CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON saved_notifications(created_by);
        CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON saved_notifications(is_active);
        CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON notification_broadcasts(sent_by);
        CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON notification_broadcasts(status);
        CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON notification_user_logs(broadcast_id);
        CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON notification_user_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON notification_user_logs(status);
        """

        # محاولة إنشاء الجداول باستخدام طرق مختلفة
        # الطريقة الأولى: استخدام RPC إذا كان متوفراً
        try:
            url = f"{SUPABASE_URL}/rpc/execute_sql"
            payload = {"sql_query": create_notifications_sql}
            response = safe_supabase_request('POST', url, json=payload)

            if response and response.status_code in [200, 201]:
                logger.info("✅ تم إنشاء جداول نظام الإشعارات بنجاح باستخدام RPC")
                return True
        except Exception as e:
            logger.warning(f"فشل في استخدام RPC لإنشاء الجداول: {e}")

        # الطريقة الثانية: إنشاء الجداول واحداً تلو الآخر باستخدام REST API
        try:
            logger.info("محاولة إنشاء الجداول واحداً تلو الآخر...")

            # إنشاء الجداول باستخدام SQL مباشر عبر REST API
            success = create_tables_individually()
            if success:
                logger.info("✅ تم إنشاء جداول نظام الإشعارات بنجاح")
                return True

        except Exception as e:
            logger.error(f"فشل في إنشاء الجداول: {e}")

        # الطريقة الثالثة: التحقق من وجود الجداول فقط
        try:
            logger.info("محاولة التحقق من وجود الجداول...")

            # التحقق من وجود الجداول أولاً
            tables_to_check = ['saved_notifications', 'notification_broadcasts', 'notification_user_logs']

            for table_name in tables_to_check:
                try:
                    # محاولة الوصول للجدول للتحقق من وجوده
                    test_url = f"{SUPABASE_URL}/rest/v1/{table_name}?limit=1"
                    test_response = safe_supabase_request('GET', test_url)

                    if test_response and test_response.status_code == 200:
                        logger.info(f"✅ الجدول {table_name} موجود بالفعل")
                    else:
                        logger.info(f"⚠️ الجدول {table_name} غير موجود أو غير متاح")

                except Exception as e:
                    logger.warning(f"تعذر التحقق من الجدول {table_name}: {e}")

            # إذا وصلنا هنا، فالجداول موجودة أو يمكن الوصول إليها
            logger.info("✅ تم التحقق من جداول نظام الإشعارات")
            return True

        except Exception as e:
            logger.error(f"فشل في التحقق من الجداول: {e}")

        # الطريقة الرابعة: تجاهل الخطأ والمتابعة
        logger.warning("⚠️ تعذر إنشاء/التحقق من جداول الإشعارات، ولكن سيتم المتابعة")
        return True  # إرجاع True للسماح للبوت بالعمل

    except Exception as e:
        logger.error(f"خطأ في إنشاء جداول نظام الإشعارات: {e}")
        return False

def create_tables_individually():
    """
    إنشاء الجداول واحداً تلو الآخر باستخدام REST API
    """
    try:
        logger.info("بدء إنشاء الجداول واحداً تلو الآخر...")

        # قائمة الجداول مع SQL الخاص بكل جدول
        tables_sql = {
            'saved_notifications': """
                CREATE TABLE IF NOT EXISTS public.saved_notifications (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true
                );
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON public.saved_notifications(created_by);
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON public.saved_notifications(is_active);
            """,

            'notification_broadcasts': """
                CREATE TABLE IF NOT EXISTS public.notification_broadcasts (
                    id SERIAL PRIMARY KEY,
                    notification_id INTEGER,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text',
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    sent_by TEXT NOT NULL,
                    total_users INTEGER DEFAULT 0,
                    successful_sends INTEGER DEFAULT 0,
                    failed_sends INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON public.notification_broadcasts(sent_by);
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON public.notification_broadcasts(status);
            """,

            'notification_user_logs': """
                CREATE TABLE IF NOT EXISTS public.notification_user_logs (
                    id SERIAL PRIMARY KEY,
                    broadcast_id INTEGER,
                    user_id TEXT NOT NULL,
                    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON public.notification_user_logs(broadcast_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON public.notification_user_logs(user_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON public.notification_user_logs(status);
            """
        }

        # محاولة إنشاء كل جدول على حدة
        created_tables = []
        for table_name, sql in tables_sql.items():
            try:
                logger.info(f"محاولة إنشاء الجدول {table_name}...")

                # محاولة تنفيذ SQL مباشرة
                success = execute_sql_directly(sql)
                if success:
                    logger.info(f"✅ تم إنشاء الجدول {table_name} بنجاح")
                    created_tables.append(table_name)
                else:
                    logger.warning(f"⚠️ فشل في إنشاء الجدول {table_name}")

            except Exception as e:
                logger.warning(f"خطأ في إنشاء الجدول {table_name}: {e}")

        # التحقق من النتائج
        if len(created_tables) > 0:
            logger.info(f"✅ تم إنشاء {len(created_tables)} جدول بنجاح: {', '.join(created_tables)}")
            return True
        else:
            logger.warning("⚠️ لم يتم إنشاء أي جدول")
            return False

    except Exception as e:
        logger.error(f"خطأ في إنشاء الجداول واحداً تلو الآخر: {e}")
        return False

def execute_sql_directly(sql_query):
    """
    تنفيذ استعلام SQL مباشرة باستخدام طرق مختلفة
    """
    try:
        # الطريقة الأولى: استخدام RPC
        try:
            url = f"{SUPABASE_URL}/rpc/exec_sql"
            payload = {"query": sql_query}
            response = safe_supabase_request('POST', url, json=payload)

            if response and response.status_code in [200, 201]:
                logger.debug("تم تنفيذ SQL باستخدام RPC")
                return True
        except Exception:
            pass

        # الطريقة الثانية: استخدام RPC بصيغة مختلفة
        try:
            url = f"{SUPABASE_URL}/rpc/execute_sql"
            payload = {"sql": sql_query}
            response = safe_supabase_request('POST', url, json=payload)

            if response and response.status_code in [200, 201]:
                logger.debug("تم تنفيذ SQL باستخدام RPC (صيغة 2)")
                return True
        except Exception:
            pass

        # الطريقة الثالثة: حفظ SQL في ملف للتنفيذ اليدوي
        try:
            sql_filename = f"create_missing_tables_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            with open(sql_filename, 'w', encoding='utf-8') as f:
                f.write(f"-- SQL لإنشاء الجداول المفقودة\n")
                f.write(f"-- تاريخ الإنشاء: {datetime.now()}\n\n")
                f.write(sql_query)

            logger.info(f"تم حفظ SQL في ملف {sql_filename} للتنفيذ اليدوي")
            return False  # لم يتم التنفيذ تلقائياً

        except Exception as e:
            logger.warning(f"فشل في حفظ SQL في ملف: {e}")

        return False

    except Exception as e:
        logger.error(f"خطأ في تنفيذ SQL: {e}")
        return False

def create_all_missing_tables():
    """
    إنشاء جميع الجداول المفقودة تلقائياً
    """
    try:
        logger.info("🔧 بدء إنشاء جميع الجداول المفقودة...")

        # قائمة شاملة بجميع الجداول المطلوبة
        all_tables_sql = {
            'mods': """
                CREATE TABLE IF NOT EXISTS public.mods (
                    id SERIAL PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    description_ar TEXT,
                    telegram_description_ar TEXT,
                    telegram_description_en TEXT,
                    category TEXT NOT NULL CHECK (category IN ('addons', 'shaders', 'texture_packs', 'seeds', 'maps')),
                    version TEXT NOT NULL,
                    download_url TEXT NOT NULL,
                    image_urls TEXT[],
                    file_size TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true,
                    download_count INTEGER DEFAULT 0,
                    rating DECIMAL(3,2) DEFAULT 0.00,
                    tags TEXT[],
                    author TEXT,
                    mod_type TEXT DEFAULT 'mod',
                    compatibility TEXT[],
                    requirements TEXT,
                    installation_guide TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_mods_category ON public.mods(category);
                CREATE INDEX IF NOT EXISTS idx_mods_version ON public.mods(version);
                CREATE INDEX IF NOT EXISTS idx_mods_active ON public.mods(is_active);
                CREATE INDEX IF NOT EXISTS idx_mods_created_at ON public.mods(created_at);
                CREATE INDEX IF NOT EXISTS idx_mods_name ON public.mods(name);
            """,

            'saved_notifications': """
                CREATE TABLE IF NOT EXISTS public.saved_notifications (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true
                );
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON public.saved_notifications(created_by);
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON public.saved_notifications(is_active);
            """,

            'notification_broadcasts': """
                CREATE TABLE IF NOT EXISTS public.notification_broadcasts (
                    id SERIAL PRIMARY KEY,
                    notification_id INTEGER,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text',
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    sent_by TEXT NOT NULL,
                    total_users INTEGER DEFAULT 0,
                    successful_sends INTEGER DEFAULT 0,
                    failed_sends INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON public.notification_broadcasts(sent_by);
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON public.notification_broadcasts(status);
            """,

            'notification_user_logs': """
                CREATE TABLE IF NOT EXISTS public.notification_user_logs (
                    id SERIAL PRIMARY KEY,
                    broadcast_id INTEGER,
                    user_id TEXT NOT NULL,
                    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON public.notification_user_logs(broadcast_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON public.notification_user_logs(user_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON public.notification_user_logs(status);
            """,

            'tasks': """
                CREATE TABLE IF NOT EXISTS public.tasks (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    task_type TEXT NOT NULL CHECK (task_type IN ('join_channel', 'visit_link', 'share_content', 'custom')),
                    target_url TEXT,
                    channel_username TEXT,
                    reward_points INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP WITH TIME ZONE,
                    max_completions INTEGER,
                    current_completions INTEGER DEFAULT 0
                );
                CREATE INDEX IF NOT EXISTS idx_tasks_active ON public.tasks(is_active);
                CREATE INDEX IF NOT EXISTS idx_tasks_type ON public.tasks(task_type);
            """,

            'user_task_completions': """
                CREATE TABLE IF NOT EXISTS public.user_task_completions (
                    id SERIAL PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    task_id INTEGER,
                    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    verified BOOLEAN DEFAULT false,
                    points_awarded INTEGER DEFAULT 0,
                    UNIQUE(user_id, task_id)
                );
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_user_id ON public.user_task_completions(user_id);
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_task_id ON public.user_task_completions(task_id);
            """
        }

        # محاولة إنشاء كل جدول
        created_count = 0
        total_count = len(all_tables_sql)

        for table_name, sql in all_tables_sql.items():
            try:
                logger.info(f"📋 إنشاء الجدول {table_name}...")
                success = execute_sql_directly(sql)
                if success:
                    logger.info(f"✅ تم إنشاء الجدول {table_name}")
                    created_count += 1
                else:
                    logger.warning(f"⚠️ فشل في إنشاء الجدول {table_name}")

            except Exception as e:
                logger.warning(f"خطأ في إنشاء الجدول {table_name}: {e}")

        # النتائج النهائية
        logger.info(f"📊 تم إنشاء {created_count}/{total_count} جدول بنجاح")

        if created_count > 0:
            logger.info("🎉 تم إنشاء بعض الجداول بنجاح!")
            return True
        else:
            logger.warning("⚠️ لم يتم إنشاء أي جدول تلقائياً")
            # إنشاء ملف SQL شامل للتنفيذ اليدوي
            create_comprehensive_sql_file(all_tables_sql)
            return False

    except Exception as e:
        logger.error(f"خطأ في إنشاء جميع الجداول: {e}")
        return False

def create_comprehensive_sql_file(tables_sql):
    """
    إنشاء ملف SQL شامل يحتوي على جميع الجداول
    """
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"create_all_missing_tables_{timestamp}.sql"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("-- ===================================================================\n")
            f.write("-- ملف SQL شامل لإنشاء جميع الجداول المفقودة\n")
            f.write("-- Comprehensive SQL file to create all missing tables\n")
            f.write("-- ===================================================================\n\n")
            f.write(f"-- تاريخ الإنشاء: {datetime.now()}\n")
            f.write(f"-- Creation date: {datetime.now()}\n\n")

            for table_name, sql in tables_sql.items():
                f.write(f"-- ===================================================================\n")
                f.write(f"-- جدول {table_name}\n")
                f.write(f"-- Table {table_name}\n")
                f.write(f"-- ===================================================================\n\n")
                f.write(sql)
                f.write("\n\n")

            f.write("-- ===================================================================\n")
            f.write("-- انتهى الملف\n")
            f.write("-- End of file\n")
            f.write("-- ===================================================================\n")

        logger.info(f"📄 تم إنشاء ملف SQL شامل: {filename}")
        logger.info("📋 يمكنك تنفيذ هذا الملف في Supabase SQL Editor لإنشاء جميع الجداول")

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف SQL الشامل: {e}")

def check_required_tables():
    """
    التحقق من وجود الجداول المطلوبة
    """
    try:
        logger.info("🔍 فحص الجداول المطلوبة...")

        required_tables = [
            'mods',
            'saved_notifications',
            'notification_broadcasts',
            'notification_user_logs',
            'tasks',
            'user_task_completions'
        ]

        existing_tables = []
        missing_tables = []

        for table_name in required_tables:
            try:
                # محاولة الوصول للجدول
                url = f"{SUPABASE_URL}/rest/v1/{table_name}?limit=1"
                response = safe_supabase_request('GET', url)

                if response and response.status_code == 200:
                    existing_tables.append(table_name)
                    logger.debug(f"✅ الجدول {table_name}: موجود")
                else:
                    missing_tables.append(table_name)
                    logger.debug(f"❌ الجدول {table_name}: مفقود")

            except Exception as e:
                missing_tables.append(table_name)
                logger.debug(f"❌ خطأ في فحص الجدول {table_name}: {e}")

        # النتائج
        total_tables = len(required_tables)
        existing_count = len(existing_tables)
        missing_count = len(missing_tables)

        logger.info(f"📊 نتائج الفحص: {existing_count}/{total_tables} جدول موجود")

        if missing_count > 0:
            logger.warning(f"⚠️ الجداول المفقودة ({missing_count}): {', '.join(missing_tables)}")
        else:
            logger.info("✅ جميع الجداول المطلوبة موجودة")

        return {
            'all_exist': missing_count == 0,
            'existing': existing_tables,
            'missing': missing_tables,
            'total': total_tables,
            'existing_count': existing_count,
            'missing_count': missing_count
        }

    except Exception as e:
        logger.error(f"خطأ في فحص الجداول المطلوبة: {e}")
        return {
            'all_exist': False,
            'existing': [],
            'missing': required_tables,
            'total': len(required_tables),
            'existing_count': 0,
            'missing_count': len(required_tables)
        }

def create_specific_missing_tables(missing_tables):
    """
    إنشاء جداول محددة فقط
    """
    try:
        if not missing_tables:
            logger.info("✅ لا توجد جداول مفقودة")
            return True

        logger.info(f"🔧 إنشاء الجداول المفقودة: {', '.join(missing_tables)}")

        # قاموس الجداول مع SQL الخاص بكل جدول
        tables_sql = {
            'mods': """
                CREATE TABLE IF NOT EXISTS public.mods (
                    id SERIAL PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    description_ar TEXT,
                    telegram_description_ar TEXT,
                    telegram_description_en TEXT,
                    category TEXT NOT NULL CHECK (category IN ('addons', 'shaders', 'texture_packs', 'seeds', 'maps')),
                    version TEXT NOT NULL,
                    download_url TEXT NOT NULL,
                    image_urls TEXT[],
                    file_size TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true,
                    download_count INTEGER DEFAULT 0,
                    rating DECIMAL(3,2) DEFAULT 0.00,
                    tags TEXT[],
                    author TEXT,
                    mod_type TEXT DEFAULT 'mod',
                    compatibility TEXT[],
                    requirements TEXT,
                    installation_guide TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_mods_category ON public.mods(category);
                CREATE INDEX IF NOT EXISTS idx_mods_version ON public.mods(version);
                CREATE INDEX IF NOT EXISTS idx_mods_active ON public.mods(is_active);
                CREATE INDEX IF NOT EXISTS idx_mods_created_at ON public.mods(created_at);
                CREATE INDEX IF NOT EXISTS idx_mods_name ON public.mods(name);
            """,

            'saved_notifications': """
                CREATE TABLE IF NOT EXISTS public.saved_notifications (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true
                );
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON public.saved_notifications(created_by);
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON public.saved_notifications(is_active);
            """,

            'notification_broadcasts': """
                CREATE TABLE IF NOT EXISTS public.notification_broadcasts (
                    id SERIAL PRIMARY KEY,
                    notification_id INTEGER,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text',
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    sent_by TEXT NOT NULL,
                    total_users INTEGER DEFAULT 0,
                    successful_sends INTEGER DEFAULT 0,
                    failed_sends INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON public.notification_broadcasts(sent_by);
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON public.notification_broadcasts(status);
            """,

            'notification_user_logs': """
                CREATE TABLE IF NOT EXISTS public.notification_user_logs (
                    id SERIAL PRIMARY KEY,
                    broadcast_id INTEGER,
                    user_id TEXT NOT NULL,
                    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON public.notification_user_logs(broadcast_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON public.notification_user_logs(user_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON public.notification_user_logs(status);
            """,

            'tasks': """
                CREATE TABLE IF NOT EXISTS public.tasks (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    task_type TEXT NOT NULL CHECK (task_type IN ('join_channel', 'visit_link', 'share_content', 'custom')),
                    target_url TEXT,
                    channel_username TEXT,
                    reward_points INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP WITH TIME ZONE,
                    max_completions INTEGER,
                    current_completions INTEGER DEFAULT 0
                );
                CREATE INDEX IF NOT EXISTS idx_tasks_active ON public.tasks(is_active);
                CREATE INDEX IF NOT EXISTS idx_tasks_type ON public.tasks(task_type);
            """,

            'user_task_completions': """
                CREATE TABLE IF NOT EXISTS public.user_task_completions (
                    id SERIAL PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    task_id INTEGER,
                    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    verified BOOLEAN DEFAULT false,
                    points_awarded INTEGER DEFAULT 0,
                    UNIQUE(user_id, task_id)
                );
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_user_id ON public.user_task_completions(user_id);
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_task_id ON public.user_task_completions(task_id);
            """
        }

        # إنشاء الجداول المفقودة فقط
        created_count = 0
        for table_name in missing_tables:
            if table_name in tables_sql:
                try:
                    logger.info(f"📋 إنشاء الجدول المفقود: {table_name}")
                    sql = tables_sql[table_name]

                    # محاولة إنشاء الجدول
                    success = execute_sql_directly(sql)
                    if success:
                        logger.info(f"✅ تم إنشاء الجدول {table_name}")
                        created_count += 1
                    else:
                        logger.warning(f"⚠️ فشل في إنشاء الجدول {table_name}")

                except Exception as e:
                    logger.warning(f"خطأ في إنشاء الجدول {table_name}: {e}")

        # النتائج
        if created_count > 0:
            logger.info(f"✅ تم إنشاء {created_count}/{len(missing_tables)} جدول بنجاح")
            return True
        else:
            logger.warning("⚠️ لم يتم إنشاء أي جدول")
            return False

    except Exception as e:
        logger.error(f"خطأ في إنشاء الجداول المحددة: {e}")
        return False

def save_notification_template(admin_id: str, title: str, message: str, message_type: str = 'text',
                             media_url: str = None, button_text: str = None, button_url: str = None) -> Optional[int]:
    """
    حفظ قالب إشعار جديد
    Args:
        admin_id: معرف الأدمن
        title: عنوان الإشعار
        message: نص الإشعار
        message_type: نوع الرسالة (text, photo, video, document)
        media_url: رابط الوسائط (اختياري)
        button_text: نص الزر (اختياري)
        button_url: رابط الزر (اختياري)
    Returns: معرف الإشعار المحفوظ أو None
    """
    try:
        logger.info(f"جاري حفظ قالب إشعار جديد بواسطة الأدمن {admin_id}")

        notification_data = {
            'title': title,
            'message': message,
            'message_type': message_type,
            'media_url': media_url,
            'button_text': button_text,
            'button_url': button_url,
            'created_by': admin_id,
            'created_at': 'now()',
            'updated_at': 'now()'
        }

        url = f"{SUPABASE_URL}/rest/v1/saved_notifications"
        response = requests.post(url, headers=HEADERS, json=notification_data, timeout=10)

        if response.status_code == 201:
            data = response.json()
            notification_id = data[0]['id'] if data else None
            logger.info(f"تم حفظ قالب الإشعار بنجاح - ID: {notification_id}")
            return notification_id
        else:
            logger.error(f"فشل في حفظ قالب الإشعار: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في حفظ قالب الإشعار: {e}")
        return None

def get_saved_notifications(admin_id: str = None, limit: int = 50, offset: int = 0) -> List[Dict]:
    """
    جلب قوالب الإشعارات المحفوظة
    Args:
        admin_id: معرف الأدمن (اختياري للفلترة)
        limit: عدد النتائج
        offset: نقطة البداية
    Returns: قائمة بقوالب الإشعارات
    """
    try:
        logger.info(f"جاري جلب قوالب الإشعارات المحفوظة")

        url = f"{SUPABASE_URL}/rest/v1/saved_notifications?is_active=eq.true&limit={limit}&offset={offset}&order=created_at.desc"

        if admin_id:
            url += f"&created_by=eq.{admin_id}"

        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} قالب إشعار")
            return data
        else:
            logger.warning(f"فشل في جلب قوالب الإشعارات: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب قوالب الإشعارات: {e}")
        return []

def get_notification_by_id(notification_id: int) -> Optional[Dict]:
    """
    جلب قالب إشعار محدد بالمعرف
    Args:
        notification_id: معرف الإشعار
    Returns: بيانات الإشعار أو None
    """
    try:
        logger.info(f"جاري جلب قالب الإشعار {notification_id}")

        url = f"{SUPABASE_URL}/rest/v1/saved_notifications?id=eq.{notification_id}&is_active=eq.true"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب قالب الإشعار {notification_id}")
                return data[0]
            else:
                logger.warning(f"لم يتم العثور على قالب الإشعار {notification_id}")
                return None
        else:
            logger.warning(f"فشل في جلب قالب الإشعار {notification_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب قالب الإشعار {notification_id}: {e}")
        return None

def update_notification_template(notification_id: int, admin_id: str, **updates) -> bool:
    """
    تحديث قالب إشعار موجود
    Args:
        notification_id: معرف الإشعار
        admin_id: معرف الأدمن
        **updates: البيانات المراد تحديثها
    Returns: True إذا تم التحديث بنجاح
    """
    try:
        logger.info(f"جاري تحديث قالب الإشعار {notification_id} بواسطة الأدمن {admin_id}")

        # التحقق من ملكية الإشعار
        notification = get_notification_by_id(notification_id)
        if not notification or notification['created_by'] != admin_id:
            logger.warning(f"الأدمن {admin_id} لا يملك صلاحية تحديث الإشعار {notification_id}")
            return False

        # إضافة تاريخ التحديث
        updates['updated_at'] = 'now()'

        url = f"{SUPABASE_URL}/rest/v1/saved_notifications?id=eq.{notification_id}"
        response = requests.patch(url, headers=HEADERS, json=updates, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم تحديث قالب الإشعار {notification_id} بنجاح")
            return True
        else:
            logger.error(f"فشل في تحديث قالب الإشعار {notification_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تحديث قالب الإشعار {notification_id}: {e}")
        return False

def delete_notification_template(notification_id: int, admin_id: str) -> bool:
    """
    حذف قالب إشعار (حذف منطقي)
    Args:
        notification_id: معرف الإشعار
        admin_id: معرف الأدمن
    Returns: True إذا تم الحذف بنجاح
    """
    try:
        logger.info(f"جاري حذف قالب الإشعار {notification_id} بواسطة الأدمن {admin_id}")

        # التحقق من ملكية الإشعار
        notification = get_notification_by_id(notification_id)
        if not notification or notification['created_by'] != admin_id:
            logger.warning(f"الأدمن {admin_id} لا يملك صلاحية حذف الإشعار {notification_id}")
            return False

        # حذف منطقي
        updates = {
            'is_active': False,
            'updated_at': 'now()'
        }

        url = f"{SUPABASE_URL}/rest/v1/saved_notifications?id=eq.{notification_id}"
        response = requests.patch(url, headers=HEADERS, json=updates, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف قالب الإشعار {notification_id} بنجاح")
            return True
        else:
            logger.error(f"فشل في حذف قالب الإشعار {notification_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف قالب الإشعار {notification_id}: {e}")
        return False

def create_broadcast(notification_id: int, admin_id: str, total_users: int) -> Optional[int]:
    """
    إنشاء عملية بث جديدة
    Args:
        notification_id: معرف قالب الإشعار
        admin_id: معرف الأدمن
        total_users: العدد الإجمالي للمستخدمين
    Returns: معرف عملية البث أو None
    """
    try:
        logger.info(f"جاري إنشاء عملية بث للإشعار {notification_id} بواسطة الأدمن {admin_id}")

        # جلب بيانات الإشعار
        notification = get_notification_by_id(notification_id)
        if not notification:
            logger.error(f"لم يتم العثور على قالب الإشعار {notification_id}")
            return None

        broadcast_data = {
            'notification_id': notification_id,
            'title': notification['title'],
            'message': notification['message'],
            'message_type': notification['message_type'],
            'media_url': notification['media_url'],
            'button_text': notification['button_text'],
            'button_url': notification['button_url'],
            'sent_by': admin_id,
            'total_users': total_users,
            'started_at': 'now()',
            'status': 'pending'
        }

        url = f"{SUPABASE_URL}/rest/v1/notification_broadcasts"
        response = requests.post(url, headers=HEADERS, json=broadcast_data, timeout=10)

        if response.status_code == 201:
            data = response.json()
            broadcast_id = data[0]['id'] if data else None
            logger.info(f"تم إنشاء عملية البث بنجاح - ID: {broadcast_id}")
            return broadcast_id
        else:
            logger.error(f"فشل في إنشاء عملية البث: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في إنشاء عملية البث: {e}")
        return None

def update_broadcast_status(broadcast_id: int, status: str, successful_sends: int = None,
                          failed_sends: int = None, error_message: str = None) -> bool:
    """
    تحديث حالة عملية البث
    Args:
        broadcast_id: معرف عملية البث
        status: الحالة الجديدة
        successful_sends: عدد الإرسالات الناجحة
        failed_sends: عدد الإرسالات الفاشلة
        error_message: رسالة الخطأ (اختياري)
    Returns: True إذا تم التحديث بنجاح
    """
    try:
        logger.info(f"جاري تحديث حالة عملية البث {broadcast_id} إلى {status}")

        updates = {'status': status}

        if successful_sends is not None:
            updates['successful_sends'] = successful_sends
        if failed_sends is not None:
            updates['failed_sends'] = failed_sends
        if error_message:
            updates['error_message'] = error_message
        if status == 'completed':
            updates['completed_at'] = 'now()'

        url = f"{SUPABASE_URL}/rest/v1/notification_broadcasts?id=eq.{broadcast_id}"
        response = requests.patch(url, headers=HEADERS, json=updates, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم تحديث حالة عملية البث {broadcast_id} بنجاح")
            return True
        else:
            logger.error(f"فشل في تحديث حالة عملية البث {broadcast_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تحديث حالة عملية البث {broadcast_id}: {e}")
        return False

def log_user_notification(broadcast_id: int, user_id: str, status: str, error_message: str = None) -> bool:
    """
    تسجيل حالة إرسال الإشعار لمستخدم معين
    Args:
        broadcast_id: معرف عملية البث
        user_id: معرف المستخدم
        status: حالة الإرسال
        error_message: رسالة الخطأ (اختياري)
    Returns: True إذا تم التسجيل بنجاح
    """
    try:
        log_data = {
            'broadcast_id': broadcast_id,
            'user_id': user_id,
            'status': status,
            'sent_at': 'now()'
        }

        if error_message:
            log_data['error_message'] = error_message

        url = f"{SUPABASE_URL}/rest/v1/notification_user_logs"
        response = requests.post(url, headers=HEADERS, json=log_data, timeout=10)

        return response.status_code == 201

    except Exception as e:
        logger.error(f"خطأ في تسجيل حالة إرسال الإشعار للمستخدم {user_id}: {e}")
        return False

def get_broadcast_history(admin_id: str = None, limit: int = 50, offset: int = 0) -> List[Dict]:
    """
    جلب تاريخ عمليات البث
    Args:
        admin_id: معرف الأدمن (اختياري للفلترة)
        limit: عدد النتائج
        offset: نقطة البداية
    Returns: قائمة بعمليات البث
    """
    try:
        logger.info(f"جاري جلب تاريخ عمليات البث")

        url = f"{SUPABASE_URL}/rest/v1/notification_broadcasts?limit={limit}&offset={offset}&order=started_at.desc"

        if admin_id:
            url += f"&sent_by=eq.{admin_id}"

        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} عملية بث")
            return data
        else:
            logger.warning(f"فشل في جلب تاريخ عمليات البث: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ عمليات البث: {e}")
        return []

def get_broadcast_details(broadcast_id: int) -> Optional[Dict]:
    """
    جلب تفاصيل عملية بث محددة
    Args:
        broadcast_id: معرف عملية البث
    Returns: تفاصيل عملية البث أو None
    """
    try:
        logger.info(f"جاري جلب تفاصيل عملية البث {broadcast_id}")

        url = f"{SUPABASE_URL}/rest/v1/notification_broadcasts?id=eq.{broadcast_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                logger.info(f"تم جلب تفاصيل عملية البث {broadcast_id}")
                return data[0]
            else:
                logger.warning(f"لم يتم العثور على عملية البث {broadcast_id}")
                return None
        else:
            logger.warning(f"فشل في جلب تفاصيل عملية البث {broadcast_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل عملية البث {broadcast_id}: {e}")
        return None

def get_notification_stats() -> Dict:
    """
    جلب إحصائيات نظام الإشعارات
    Returns: إحصائيات شاملة
    """
    try:
        logger.info("جاري جلب إحصائيات نظام الإشعارات")

        # إحصائيات قوالب الإشعارات
        notifications_url = f"{SUPABASE_URL}/rest/v1/saved_notifications?select=count&is_active=eq.true"
        notifications_response = requests.get(notifications_url, headers=HEADERS, timeout=10)

        # إحصائيات عمليات البث
        broadcasts_url = f"{SUPABASE_URL}/rest/v1/notification_broadcasts?select=count,status"
        broadcasts_response = requests.get(broadcasts_url, headers=HEADERS, timeout=10)

        # إحصائيات الإرسال
        logs_url = f"{SUPABASE_URL}/rest/v1/notification_user_logs?select=count,status"
        logs_response = requests.get(logs_url, headers=HEADERS, timeout=10)

        stats = {
            "total_saved_notifications": 0,
            "total_broadcasts": 0,
            "completed_broadcasts": 0,
            "pending_broadcasts": 0,
            "failed_broadcasts": 0,
            "total_sent_notifications": 0,
            "successful_sends": 0,
            "failed_sends": 0
        }

        if notifications_response.status_code == 200:
            notifications_data = notifications_response.json()
            stats["total_saved_notifications"] = len(notifications_data)

        if broadcasts_response.status_code == 200:
            broadcasts_data = broadcasts_response.json()
            stats["total_broadcasts"] = len(broadcasts_data)

            for broadcast in broadcasts_data:
                if broadcast.get('status') == 'completed':
                    stats["completed_broadcasts"] += 1
                elif broadcast.get('status') == 'pending':
                    stats["pending_broadcasts"] += 1
                elif broadcast.get('status') == 'failed':
                    stats["failed_broadcasts"] += 1

        if logs_response.status_code == 200:
            logs_data = logs_response.json()
            stats["total_sent_notifications"] = len(logs_data)

            for log in logs_data:
                if log.get('status') == 'sent':
                    stats["successful_sends"] += 1
                elif log.get('status') == 'failed':
                    stats["failed_sends"] += 1

        logger.info("تم جلب إحصائيات نظام الإشعارات بنجاح")
        return stats

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات نظام الإشعارات: {e}")
        return {
            "total_saved_notifications": 0,
            "total_broadcasts": 0,
            "completed_broadcasts": 0,
            "pending_broadcasts": 0,
            "failed_broadcasts": 0,
            "total_sent_notifications": 0,
            "successful_sends": 0,
            "failed_sends": 0
        }

# --- دوال حذف بيانات المستخدم بالكامل ---

def delete_user_ads_settings(user_id: str) -> bool:
    """حذف إعدادات الإعلانات للمستخدم"""
    try:
        logger.info(f"جاري حذف إعدادات الإعلانات للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف إعدادات الإعلانات للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.info(f"لا توجد إعدادات إعلانات للمستخدم {user_id}")
            return True  # اعتبر النجاح إذا لم توجد بيانات
        else:
            logger.error(f"فشل في حذف إعدادات الإعلانات للمستخدم {user_id}: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"خطأ في حذف إعدادات الإعلانات للمستخدم {user_id}: {e}")
        return False

def get_user_tasks_settings(user_id: str) -> Optional[Dict]:
    """جلب إعدادات المهام للمستخدم"""
    try:
        logger.info(f"جاري جلب إعدادات المهام للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_tasks_settings?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                return data[0]
            return None
        else:
            logger.warning(f"فشل في جلب إعدادات المهام للمستخدم {user_id}: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"خطأ في جلب إعدادات المهام للمستخدم {user_id}: {e}")
        return None

def delete_user_tasks_settings(user_id: str) -> bool:
    """حذف إعدادات المهام للمستخدم"""
    try:
        logger.info(f"جاري حذف إعدادات المهام للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_tasks_settings?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف إعدادات المهام للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.info(f"لا توجد إعدادات مهام للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حذف إعدادات المهام للمستخدم {user_id}: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"خطأ في حذف إعدادات المهام للمستخدم {user_id}: {e}")
        return False

def get_user_custom_download_links(user_id: str) -> List[Dict]:
    """جلب الروابط المخصصة للمستخدم"""
    try:
        logger.info(f"جاري جلب الروابط المخصصة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/custom_download_links?created_by=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"تم جلب {len(data)} رابط مخصص للمستخدم {user_id}")
            return data
        else:
            logger.warning(f"فشل في جلب الروابط المخصصة للمستخدم {user_id}: {response.status_code}")
            return []
    except Exception as e:
        logger.error(f"خطأ في جلب الروابط المخصصة للمستخدم {user_id}: {e}")
        return []

def delete_user_custom_download_links(user_id: str) -> bool:
    """حذف الروابط المخصصة للمستخدم"""
    try:
        logger.info(f"جاري حذف الروابط المخصصة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/custom_download_links?created_by=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف الروابط المخصصة للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.info(f"لا توجد روابط مخصصة للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حذف الروابط المخصصة للمستخدم {user_id}: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"خطأ في حذف الروابط المخصصة للمستخدم {user_id}: {e}")
        return False

def get_user_page_customization_settings(user_id: str) -> Optional[Dict]:
    """جلب إعدادات تخصيص الصفحة للمستخدم"""
    try:
        logger.info(f"جاري جلب إعدادات تخصيص الصفحة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                return data[0]
            return None
        else:
            logger.warning(f"فشل في جلب إعدادات تخصيص الصفحة للمستخدم {user_id}: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"خطأ في جلب إعدادات تخصيص الصفحة للمستخدم {user_id}: {e}")
        return None

def delete_user_page_customization_settings(user_id: str) -> bool:
    """حذف إعدادات تخصيص الصفحة للمستخدم"""
    try:
        logger.info(f"جاري حذف إعدادات تخصيص الصفحة للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف إعدادات تخصيص الصفحة للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.info(f"لا توجد إعدادات تخصيص صفحة للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حذف إعدادات تخصيص الصفحة للمستخدم {user_id}: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"خطأ في حذف إعدادات تخصيص الصفحة للمستخدم {user_id}: {e}")
        return False

def get_user_invitation_data(user_id: str) -> Optional[Dict]:
    """جلب بيانات الدعوات للمستخدم"""
    try:
        logger.info(f"جاري جلب بيانات الدعوات للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_invitations?user_id=eq.{user_id}"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data:
                return data[0]
            return None
        else:
            logger.warning(f"فشل في جلب بيانات الدعوات للمستخدم {user_id}: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الدعوات للمستخدم {user_id}: {e}")
        return None

def delete_user_invitation_data(user_id: str) -> bool:
    """حذف بيانات الدعوات للمستخدم"""
    try:
        logger.info(f"جاري حذف بيانات الدعوات للمستخدم {user_id}")

        url = f"{SUPABASE_URL}/rest/v1/user_invitations?user_id=eq.{user_id}"
        response = requests.delete(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info(f"تم حذف بيانات الدعوات للمستخدم {user_id}")
            return True
        elif response.status_code == 404:
            logger.info(f"لا توجد بيانات دعوات للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حذف بيانات الدعوات للمستخدم {user_id}: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"خطأ في حذف بيانات الدعوات للمستخدم {user_id}: {e}")
        return False

# --- دوال إدارة Supabase Storage ---

def create_storage_bucket(bucket_name: str = "image3") -> bool:
    """
    إنشاء حاوية جديدة في Supabase Storage
    Args:
        bucket_name: اسم الحاوية
    Returns: True إذا تم الإنشاء بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري إنشاء حاوية {bucket_name}")

        # إعداد headers باستخدام المفتاح العادي
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }

        # بيانات الحاوية
        bucket_data = {
            'id': bucket_name,
            'name': bucket_name,
            'public': True,  # جعل الحاوية عامة للوصول للصور
            'file_size_limit': 52428800,  # 50 MB
            'allowed_mime_types': ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        }

        # إنشاء الحاوية
        create_url = f"{SUPABASE_URL}/storage/v1/bucket"
        response = requests.post(create_url, headers=headers, json=bucket_data, timeout=10)

        if response.status_code in [200, 201]:
            logger.info(f"تم إنشاء حاوية {bucket_name} بنجاح")
            return True
        elif response.status_code == 409:
            logger.info(f"حاوية {bucket_name} موجودة بالفعل")
            return True
        elif "already exists" in response.text or "Duplicate" in response.text:
            logger.info(f"حاوية {bucket_name} موجودة بالفعل")
            return True
        else:
            logger.error(f"فشل في إنشاء حاوية {bucket_name}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إنشاء حاوية {bucket_name}: {e}")
        return False

def upload_image_to_storage(image_data: bytes, filename: str, bucket_name: str = "avatars") -> Optional[str]:
    """
    رفع صورة إلى Supabase Storage
    Args:
        image_data: بيانات الصورة كـ bytes
        filename: اسم الملف
        bucket_name: اسم الحاوية (افتراضي: avatars)
    Returns: رابط الصورة أو None إذا فشل الرفع
    """
    try:
        logger.info(f"جاري رفع الصورة {filename} إلى حاوية {bucket_name}")

        # محاولة إنشاء الحاوية إذا لم تكن موجودة (اختياري)
        create_storage_bucket(bucket_name)  # تجاهل النتيجة والمتابعة

        # إنشاء اسم ملف فريد
        unique_filename = f"{uuid.uuid4()}_{filename}"

        # تحديد نوع المحتوى بناءً على امتداد الملف
        content_type = 'image/jpeg'
        if filename.lower().endswith('.png'):
            content_type = 'image/png'
        elif filename.lower().endswith('.gif'):
            content_type = 'image/gif'
        elif filename.lower().endswith('.webp'):
            content_type = 'image/webp'

        # إعداد headers للرفع باستخدام المفتاح العادي
        upload_headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': content_type,
            'x-upsert': 'true'  # للسماح بالكتابة فوق الملف إذا كان موجوداً
        }

        # رفع الصورة
        upload_url = f"{SUPABASE_URL}/storage/v1/object/{bucket_name}/{unique_filename}"
        response = requests.post(upload_url, headers=upload_headers, data=image_data, timeout=30)

        if response.status_code in [200, 201]:
            # إنشاء رابط الصورة العام
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/{bucket_name}/{unique_filename}"
            logger.info(f"تم رفع الصورة بنجاح: {public_url}")
            return public_url
        else:
            logger.error(f"فشل في رفع الصورة {filename}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في رفع الصورة {filename}: {e}")
        return None

def download_telegram_image(bot, file_id: str) -> Optional[bytes]:
    """
    تحميل صورة من تيليجرام
    Args:
        bot: كائن البوت
        file_id: معرف الملف في تيليجرام
    Returns: بيانات الصورة كـ bytes أو None إذا فشل التحميل
    """
    try:
        logger.info(f"جاري تحميل الصورة من تيليجرام: {file_id}")

        # الحصول على معلومات الملف
        file = bot.get_file(file_id)

        # تحميل الصورة
        file_url = file.file_path
        if file_url.startswith('https://'):
            # رابط مباشر
            response = requests.get(file_url, timeout=30)
        else:
            # رابط تيليجرام API
            response = requests.get(f"https://api.telegram.org/file/bot{bot.token}/{file_url}", timeout=30)

        if response.status_code == 200:
            logger.info(f"تم تحميل الصورة بنجاح من تيليجرام")
            return response.content
        else:
            logger.error(f"فشل في تحميل الصورة من تيليجرام: {response.status_code}")
            return None

    except Exception as e:
        logger.error(f"خطأ في تحميل الصورة من تيليجرام: {e}")
        return None

def get_style_template_settings(style_name: str) -> Dict:
    """
    الحصول على إعدادات قالب ستايل محدد
    Args:
        style_name: اسم الستايل (telegram, tiktok, classic, professional, custom)
    Returns: إعدادات الستايل
    """
    style_templates = {
        'telegram': {
            'style_template': 'telegram',
            'page_theme': 'telegram',
            'custom_bg_color': '#0088cc',
            'custom_header_color': '#0088cc',
            'custom_text_color': '#ffffff',
            'custom_button_color': '#40a7e3',
            'custom_accent_color': '#64b5f6',
            'layout_style': 'modern',
            'enable_gradients': True,
            'enable_animations': True,
            'enable_shadows': True,
            'border_radius': 'medium',
            'custom_font_family': 'Roboto',
            'custom_font_size': 'medium'
        },
        'tiktok': {
            'style_template': 'tiktok',
            'page_theme': 'custom',
            'custom_bg_color': '#000000',
            'custom_header_color': '#ff0050',
            'custom_text_color': '#ffffff',
            'custom_button_color': '#ff0050',
            'custom_accent_color': '#25f4ee',
            'custom_card_color': '#1a1a1a',
            'layout_style': 'modern',
            'enable_gradients': True,
            'enable_animations': True,
            'enable_shadows': True,
            'border_radius': 'large',
            'custom_font_family': 'Poppins',
            'custom_font_size': 'large'
        },
        'classic': {
            'style_template': 'classic',
            'page_theme': 'custom',
            'custom_bg_color': '#f5f5dc',
            'custom_header_color': '#8b4513',
            'custom_text_color': '#2f4f4f',
            'custom_button_color': '#cd853f',
            'custom_accent_color': '#daa520',
            'custom_card_color': '#ffffff',
            'layout_style': 'spacious',
            'enable_gradients': False,
            'enable_animations': False,
            'enable_shadows': True,
            'border_radius': 'small',
            'custom_font_family': 'Georgia',
            'custom_font_size': 'medium'
        },
        'professional': {
            'style_template': 'professional',
            'page_theme': 'custom',
            'custom_bg_color': '#f8f9fa',
            'custom_header_color': '#2c3e50',
            'custom_text_color': '#2c3e50',
            'custom_button_color': '#3498db',
            'custom_accent_color': '#e74c3c',
            'custom_card_color': '#ffffff',
            'layout_style': 'minimal',
            'enable_gradients': False,
            'enable_animations': False,
            'enable_shadows': True,
            'border_radius': 'medium',
            'custom_font_family': 'Inter',
            'custom_font_size': 'medium'
        },
        'default': {
            'style_template': 'default',
            'page_theme': 'default',
            'custom_bg_color': '#1a1a1a',
            'custom_header_color': '#FFA500',
            'custom_text_color': '#ffffff',
            'custom_button_color': '#FFA500',
            'custom_accent_color': '#FFD700',
            'custom_card_color': '#2D2D2D',
            'layout_style': 'modern',
            'enable_gradients': True,
            'enable_animations': True,
            'enable_shadows': True,
            'border_radius': 'medium',
            'custom_font_family': 'Press Start 2P',
            'custom_font_size': 'medium'
        }
    }

    return style_templates.get(style_name, style_templates['default'])

def apply_style_template(user_id: str, style_name: str) -> bool:
    """
    تطبيق قالب ستايل محدد للمستخدم
    Args:
        user_id: معرف المستخدم
        style_name: اسم الستايل
    Returns: True إذا تم التطبيق بنجاح
    """
    try:
        logger.info(f"جاري تطبيق ستايل {style_name} للمستخدم {user_id}")

        # الحصول على إعدادات الستايل
        style_settings = get_style_template_settings(style_name)

        # تطبيق الإعدادات
        success = update_user_page_customization(user_id, style_settings)

        if success:
            logger.info(f"تم تطبيق ستايل {style_name} بنجاح للمستخدم {user_id}")
        else:
            logger.error(f"فشل في تطبيق ستايل {style_name} للمستخدم {user_id}")

        return success

    except Exception as e:
        logger.error(f"خطأ في تطبيق ستايل {style_name} للمستخدم {user_id}: {e}")
        return False

def create_page_customization_table_direct():
    """محاولة إنشاء جدول إعدادات تخصيص الصفحة أو التحقق من وجوده"""
    try:
        logger.info("جاري التحقق من جدول page_customization_settings...")

        # محاولة الوصول للجدول أولاً
        url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?limit=1"
        response = requests.get(url, headers=HEADERS, timeout=10)

        if response.status_code == 200:
            logger.info("جدول page_customization_settings موجود بالفعل")
            return True
        elif response.status_code == 404:
            logger.warning("جدول page_customization_settings غير موجود - يجب إنشاؤه يدوياً")
            return False
        else:
            logger.warning(f"حالة غير متوقعة للجدول: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"خطأ في التحقق من جدول page_customization_settings: {e}")
        return False

def create_page_customization_table_with_styles():
    """
    إنشاء جدول page_customization_settings مع جميع الحقول الجديدة للأساليب المتقدمة
    Creates page_customization_settings table with all new fields for advanced styles
    """
    try:
        logger.info("جاري إنشاء جدول page_customization_settings مع الأساليب المتقدمة...")

        # SQL لإنشاء الجدول الكامل مع جميع الحقول
        create_table_sql = """
        -- إنشاء جدول إعدادات تخصيص الصفحة مع الأساليب المتقدمة
        CREATE TABLE IF NOT EXISTS page_customization_settings (
            id SERIAL PRIMARY KEY,
            user_id TEXT NOT NULL UNIQUE,

            -- الإعدادات الأساسية
            site_name TEXT DEFAULT 'Modetaris',
            channel_logo_url TEXT,
            logo_position TEXT DEFAULT 'right' CHECK (logo_position IN ('left', 'right')),

            -- إعدادات الثيم والستايل
            page_theme TEXT DEFAULT 'default' CHECK (page_theme IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'dark', 'light', 'custom')),
            style_template TEXT DEFAULT 'default' CHECK (style_template IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'custom')),

            -- الألوان الأساسية
            custom_bg_color TEXT,
            custom_header_color TEXT,
            custom_text_color TEXT,
            custom_button_color TEXT,
            custom_border_color TEXT,

            -- الألوان المتقدمة
            custom_accent_color TEXT,
            custom_card_color TEXT,
            custom_shadow_color TEXT,

            -- إعدادات الخطوط
            custom_font_family TEXT DEFAULT 'Press Start 2P',
            custom_font_size TEXT DEFAULT 'medium' CHECK (custom_font_size IN ('small', 'medium', 'large', 'extra-large')),

            -- إعدادات التأثيرات
            enable_animations BOOLEAN DEFAULT true,
            enable_gradients BOOLEAN DEFAULT true,
            enable_shadows BOOLEAN DEFAULT true,

            -- إعدادات التخطيط
            layout_style TEXT DEFAULT 'modern' CHECK (layout_style IN ('modern', 'compact', 'spacious', 'minimal')),
            border_radius TEXT DEFAULT 'medium' CHECK (border_radius IN ('none', 'small', 'medium', 'large', 'round')),

            -- إعدادات العرض
            show_all_images BOOLEAN DEFAULT true,
            enable_mod_opening BOOLEAN DEFAULT true,

            -- نصوص الأزرار
            download_button_text_ar TEXT DEFAULT 'تحميل',
            download_button_text_en TEXT DEFAULT 'Download',
            open_button_text_ar TEXT DEFAULT 'فتح',
            open_button_text_en TEXT DEFAULT 'Open',

            -- نصوص التسميات
            version_label_ar TEXT DEFAULT 'الإصدار',
            version_label_en TEXT DEFAULT 'Version',
            category_label_ar TEXT DEFAULT 'تصنيف المود',
            category_label_en TEXT DEFAULT 'Mod Category',
            description_label_ar TEXT DEFAULT 'الوصف',
            description_label_en TEXT DEFAULT 'Description',

            -- الطوابع الزمنية
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- إنشاء فهارس لتحسين الأداء
        CREATE INDEX IF NOT EXISTS idx_page_customization_user_id ON page_customization_settings(user_id);
        CREATE INDEX IF NOT EXISTS idx_page_customization_style_template ON page_customization_settings(style_template);
        CREATE INDEX IF NOT EXISTS idx_page_customization_page_theme ON page_customization_settings(page_theme);
        CREATE INDEX IF NOT EXISTS idx_page_customization_layout_style ON page_customization_settings(layout_style);
        CREATE INDEX IF NOT EXISTS idx_page_customization_created_at ON page_customization_settings(created_at);

        -- إنشاء دالة لتحديث updated_at تلقائياً
        CREATE OR REPLACE FUNCTION update_page_customization_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        -- إنشاء trigger لتحديث updated_at
        DROP TRIGGER IF EXISTS trigger_update_page_customization_updated_at ON page_customization_settings;
        CREATE TRIGGER trigger_update_page_customization_updated_at
            BEFORE UPDATE ON page_customization_settings
            FOR EACH ROW
            EXECUTE FUNCTION update_page_customization_updated_at();

        -- إدراج قوالب افتراضية للأساليب المختلفة
        INSERT INTO page_customization_settings (
            user_id, site_name, style_template, page_theme,
            custom_bg_color, custom_header_color, custom_text_color,
            custom_button_color, custom_accent_color, custom_font_family,
            layout_style, enable_gradients, enable_animations
        ) VALUES
        -- قالب ستايل تيليجرام
        ('template_telegram', 'Telegram Style', 'telegram', 'telegram',
         '#0088cc', '#0088cc', '#ffffff', '#40a7e3', '#64b5f6', 'Roboto',
         'modern', true, true),

        -- قالب ستايل تيك توك
        ('template_tiktok', 'TikTok Style', 'tiktok', 'custom',
         '#000000', '#ff0050', '#ffffff', '#ff0050', '#25f4ee', 'Poppins',
         'modern', true, true),

        -- قالب ستايل كلاسيكي
        ('template_classic', 'Classic Style', 'classic', 'custom',
         '#f5f5dc', '#8b4513', '#2f4f4f', '#cd853f', '#daa520', 'Georgia',
         'spacious', false, false),

        -- قالب ستايل احترافي
        ('template_professional', 'Professional Style', 'professional', 'custom',
         '#f8f9fa', '#2c3e50', '#2c3e50', '#3498db', '#e74c3c', 'Inter',
         'minimal', false, false)

        ON CONFLICT (user_id) DO NOTHING;
        """

        # محاولة تنفيذ SQL مباشرة (إذا كان متاحاً)
        try:
            # استخدام RPC إذا كان متاحاً
            url = f"{SUPABASE_URL}/rest/v1/rpc/execute_sql"
            payload = {"sql_query": create_table_sql}
            response = safe_supabase_request('POST', url, json=payload)

            if response and response.status_code in [200, 201]:
                logger.info("تم إنشاء جدول page_customization_settings بنجاح باستخدام RPC")
                return True
            elif response:
                logger.warning(f"فشل في استخدام RPC: {response.status_code} - {response.text}")
            else:
                logger.warning("فشل في الاتصال بـ RPC")
        except Exception as rpc_error:
            logger.warning(f"RPC غير متاح: {rpc_error}")

        # إذا فشل RPC، حفظ SQL في ملف للتنفيذ اليدوي
        sql_filename = "create_page_customization_table_complete.sql"
        try:
            with open(sql_filename, 'w', encoding='utf-8') as f:
                f.write(create_table_sql)
            logger.info(f"تم حفظ SQL في ملف {sql_filename} للتنفيذ اليدوي في Supabase")

            # إرجاع False لتنبيه المستخدم أنه يحتاج لتنفيذ SQL يدوياً
            return False

        except Exception as file_error:
            logger.error(f"فشل في حفظ ملف SQL: {file_error}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إنشاء جدول page_customization_settings: {e}")
        return False

def add_missing_style_columns():
    """
    إضافة الحقول المفقودة للأساليب المتقدمة إلى جدول موجود
    Add missing style columns to existing table
    """
    try:
        logger.info("جاري إضافة الحقول المفقودة للأساليب المتقدمة...")

        # قائمة الحقول المطلوب إضافتها
        columns_to_add = [
            ("style_template", "TEXT DEFAULT 'default'"),
            ("custom_accent_color", "TEXT"),
            ("custom_card_color", "TEXT"),
            ("custom_shadow_color", "TEXT"),
            ("custom_font_family", "TEXT DEFAULT 'Press Start 2P'"),
            ("custom_font_size", "TEXT DEFAULT 'medium'"),
            ("enable_animations", "BOOLEAN DEFAULT true"),
            ("enable_gradients", "BOOLEAN DEFAULT true"),
            ("enable_shadows", "BOOLEAN DEFAULT true"),
            ("layout_style", "TEXT DEFAULT 'modern'"),
            ("border_radius", "TEXT DEFAULT 'medium'"),
            ("version_label_ar", "TEXT DEFAULT 'الإصدار'"),
            ("version_label_en", "TEXT DEFAULT 'Version'"),
            ("category_label_ar", "TEXT DEFAULT 'تصنيف المود'"),
            ("category_label_en", "TEXT DEFAULT 'Mod Category'"),
            ("description_label_ar", "TEXT DEFAULT 'الوصف'"),
            ("description_label_en", "TEXT DEFAULT 'Description'")
        ]

        # SQL لإضافة الحقول
        add_columns_sql = "-- إضافة الحقول المفقودة للأساليب المتقدمة\n"

        for column_name, column_definition in columns_to_add:
            add_columns_sql += f"""
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'page_customization_settings'
                   AND column_name = '{column_name}') THEN
        ALTER TABLE page_customization_settings ADD COLUMN {column_name} {column_definition};
        RAISE NOTICE 'Added column {column_name}';
    ELSE
        RAISE NOTICE 'Column {column_name} already exists';
    END IF;
END $$;
"""

        # إضافة القيود
        add_columns_sql += """
-- إضافة القيود للحقول الجديدة
DO $$
BEGIN
    -- قيود style_template
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'page_customization_settings_style_template_check') THEN
        ALTER TABLE page_customization_settings
        ADD CONSTRAINT page_customization_settings_style_template_check
        CHECK (style_template IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'custom'));
        RAISE NOTICE 'Added style_template constraint';
    END IF;

    -- قيود custom_font_size
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'page_customization_settings_custom_font_size_check') THEN
        ALTER TABLE page_customization_settings
        ADD CONSTRAINT page_customization_settings_custom_font_size_check
        CHECK (custom_font_size IN ('small', 'medium', 'large', 'extra-large'));
        RAISE NOTICE 'Added custom_font_size constraint';
    END IF;

    -- قيود layout_style
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'page_customization_settings_layout_style_check') THEN
        ALTER TABLE page_customization_settings
        ADD CONSTRAINT page_customization_settings_layout_style_check
        CHECK (layout_style IN ('modern', 'compact', 'spacious', 'minimal'));
        RAISE NOTICE 'Added layout_style constraint';
    END IF;

    -- قيود border_radius
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'page_customization_settings_border_radius_check') THEN
        ALTER TABLE page_customization_settings
        ADD CONSTRAINT page_customization_settings_border_radius_check
        CHECK (border_radius IN ('none', 'small', 'medium', 'large', 'round'));
        RAISE NOTICE 'Added border_radius constraint';
    END IF;
END $$;

-- إنشاء فهارس إضافية
CREATE INDEX IF NOT EXISTS idx_page_customization_style_template ON page_customization_settings(style_template);
CREATE INDEX IF NOT EXISTS idx_page_customization_layout_style ON page_customization_settings(layout_style);

-- التحقق من نجاح العملية
SELECT 'Missing columns added successfully!' as status;

-- عرض الحقول الجديدة
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'page_customization_settings'
  AND column_name IN ('style_template', 'custom_accent_color', 'layout_style', 'enable_animations')
ORDER BY column_name;
"""

        # محاولة تنفيذ SQL مباشرة
        try:
            url = f"{SUPABASE_URL}/rest/v1/rpc/execute_sql"
            payload = {"sql_query": add_columns_sql}
            response = safe_supabase_request('POST', url, json=payload)

            if response and response.status_code in [200, 201]:
                logger.info("تم إضافة الحقول المفقودة بنجاح")
                return True
            elif response:
                logger.warning(f"فشل في تنفيذ SQL مباشرة: {response.status_code} - {response.text}")
            else:
                logger.warning("فشل في الاتصال بـ RPC")
        except Exception as rpc_error:
            logger.warning(f"RPC غير متاح: {rpc_error}")

        # حفظ SQL في ملف للتنفيذ اليدوي
        sql_filename = "add_missing_style_columns.sql"
        try:
            with open(sql_filename, 'w', encoding='utf-8') as f:
                f.write(add_columns_sql)
            logger.info(f"تم حفظ SQL في ملف {sql_filename} للتنفيذ اليدوي")
            return False

        except Exception as file_error:
            logger.error(f"فشل في حفظ ملف SQL: {file_error}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إضافة الحقول المفقودة: {e}")
        return False

def check_page_customization_table_status():
    """
    فحص حالة جدول page_customization_settings والحقول المطلوبة
    Check status of page_customization_settings table and required fields
    """
    try:
        logger.info("جاري فحص حالة جدول page_customization_settings...")

        # قائمة الحقول المطلوبة للأساليب المتقدمة
        required_columns = [
            'style_template', 'custom_accent_color', 'custom_card_color',
            'custom_shadow_color', 'custom_font_family', 'custom_font_size',
            'enable_animations', 'enable_gradients', 'enable_shadows',
            'layout_style', 'border_radius'
        ]

        # التحقق من وجود الجدول
        table_url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?limit=1"
        table_response = requests.get(table_url, headers=HEADERS, timeout=10)

        status = {
            'table_exists': False,
            'missing_columns': [],
            'existing_columns': [],
            'total_records': 0,
            'recommendations': []
        }

        if table_response.status_code == 200:
            status['table_exists'] = True
            logger.info("✅ جدول page_customization_settings موجود")

            # عد السجلات
            count_url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?select=count"
            count_response = requests.get(count_url, headers={**HEADERS, "Prefer": "count=exact"}, timeout=10)
            if count_response.status_code == 200:
                total_count = int(count_response.headers.get('Content-Range', '0').split('/')[-1])
                status['total_records'] = total_count

            # فحص الحقول الموجودة
            sample_url = f"{SUPABASE_URL}/rest/v1/page_customization_settings?limit=1"
            sample_response = requests.get(sample_url, headers=HEADERS, timeout=10)

            if sample_response.status_code == 200:
                sample_data = sample_response.json()
                if sample_data:
                    existing_fields = list(sample_data[0].keys())
                    status['existing_columns'] = existing_fields

                    # تحديد الحقول المفقودة
                    for column in required_columns:
                        if column not in existing_fields:
                            status['missing_columns'].append(column)

                    if status['missing_columns']:
                        logger.warning(f"❌ حقول مفقودة: {', '.join(status['missing_columns'])}")
                        status['recommendations'].append("تشغيل دالة add_missing_style_columns()")
                    else:
                        logger.info("✅ جميع الحقول المطلوبة موجودة")
                        status['recommendations'].append("الجدول جاهز للاستخدام")
                else:
                    logger.warning("⚠️ الجدول فارغ، لا يمكن فحص الحقول")
                    status['recommendations'].append("إضافة بيانات تجريبية لفحص الحقول")

        elif table_response.status_code == 404:
            logger.error("❌ جدول page_customization_settings غير موجود")
            status['recommendations'].append("تشغيل دالة create_page_customization_table_with_styles()")
        else:
            logger.error(f"❌ خطأ في الوصول للجدول: {table_response.status_code}")
            status['recommendations'].append("التحقق من إعدادات قاعدة البيانات")

        # طباعة التقرير
        logger.info("=== تقرير حالة جدول page_customization_settings ===")
        logger.info(f"وجود الجدول: {'✅ نعم' if status['table_exists'] else '❌ لا'}")
        logger.info(f"عدد السجلات: {status['total_records']}")
        logger.info(f"الحقول الموجودة: {len(status['existing_columns'])}")
        logger.info(f"الحقول المفقودة: {len(status['missing_columns'])}")

        if status['missing_columns']:
            logger.info(f"الحقول المفقودة: {', '.join(status['missing_columns'])}")

        logger.info(f"التوصيات: {'; '.join(status['recommendations'])}")
        logger.info("=" * 50)

        return status

    except Exception as e:
        logger.error(f"خطأ في فحص حالة الجدول: {e}")
        return {
            'table_exists': False,
            'missing_columns': [],
            'existing_columns': [],
            'total_records': 0,
            'recommendations': ['فحص الاتصال بقاعدة البيانات'],
            'error': str(e)
        }

def setup_page_customization_table():
    """
    إعداد جدول page_customization_settings تلقائياً
    Setup page_customization_settings table automatically
    """
    try:
        logger.info("🚀 بدء إعداد جدول page_customization_settings...")

        # فحص الحالة الحالية
        status = check_page_customization_table_status()

        if not status['table_exists']:
            logger.info("📋 إنشاء جدول جديد...")
            success = create_page_customization_table_with_styles()
            if success:
                logger.info("✅ تم إنشاء الجدول بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في إنشاء الجدول تلقائياً، يرجى التنفيذ اليدوي")
                return False

        elif status['missing_columns']:
            logger.info("🔧 إضافة الحقول المفقودة...")
            success = add_missing_style_columns()
            if success:
                logger.info("✅ تم إضافة الحقول المفقودة بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في إضافة الحقول تلقائياً، يرجى التنفيذ اليدوي")
                return False

        else:
            logger.info("✅ الجدول جاهز ولا يحتاج إعداد إضافي")
            return True

    except Exception as e:
        logger.error(f"خطأ في إعداد الجدول: {e}")
        return False

def upload_image_to_hosting(image_data: bytes, filename: str) -> Optional[str]:
    """
    رفع صورة إلى الموقع المستضاف كحل بديل
    Args:
        image_data: بيانات الصورة
        filename: اسم الملف
    Returns: رابط الصورة على الموقع المستضاف أو None إذا فشل
    """
    try:
        import os

        # إنشاء مجلد الصور في htdocs إذا لم يكن موجوداً
        htdocs_images_dir = os.path.join("htdocs", "uploaded_images")
        if not os.path.exists(htdocs_images_dir):
            os.makedirs(htdocs_images_dir)

        # حفظ الصورة في مجلد htdocs
        file_path = os.path.join(htdocs_images_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(image_data)

        logger.info(f"تم حفظ الصورة في htdocs: {file_path}")

        # إرجاع رابط الصورة على الموقع المستضاف
        hosted_url = f"https://1c547fe5.sendaddons.pages.dev/uploaded_images/{filename}"
        logger.info(f"رابط الصورة المستضافة: {hosted_url}")

        return hosted_url

    except Exception as e:
        logger.error(f"خطأ في رفع الصورة إلى الموقع المستضاف: {e}")
        return None

def save_image_locally(image_data: bytes, filename: str) -> Optional[str]:
    """
    حفظ صورة محلي<|im_start|>اً كحل بديل
    Args:
        image_data: بيانات الصورة
        filename: اسم الملف
    Returns: مسار الصورة المحلي أو None إذا فشل
    """
    try:
        import os

        # إنشاء مجلد الصور إذا لم يكن موجوداً
        images_dir = "uploaded_images"
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)

        # حفظ الصورة محلي<|im_start|>اً
        file_path = os.path.join(images_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(image_data)

        logger.info(f"تم حفظ الصورة محلي<|im_start|>اً: {file_path}")

        # إرجاع رابط نسبي للصورة (يمكن استخدامه في الموقع المحلي)
        return f"./uploaded_images/{filename}"

    except Exception as e:
        logger.error(f"خطأ في حفظ الصورة محلي<|im_start|>اً: {e}")
        return None

async def upload_telegram_image_to_storage(bot, file_id: str, bucket_name: str = "avatars") -> Optional[str]:
    """
    رفع صورة من تيليجرام إلى Supabase Storage مع حل بديل محلي
    Args:
        bot: كائن البوت
        file_id: معرف الملف في تيليجرام
        bucket_name: اسم الحاوية (افتراضي: avatars)
    Returns: رابط الصورة أو None إذا فشل الرفع
    """
    try:
        logger.info(f"جاري رفع صورة من تيليجرام إلى Supabase Storage")

        # الحصول على معلومات الملف
        file = await bot.get_file(file_id)

        # تحميل الصورة
        file_url = file.file_path
        if file_url.startswith('https://'):
            # رابط مباشر
            response = requests.get(file_url, timeout=30)
        else:
            # رابط تيليجرام API
            response = requests.get(f"https://api.telegram.org/file/bot{bot.token}/{file_url}", timeout=30)

        if response.status_code == 200:
            # تحديد اسم الملف
            filename = f"telegram_image_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"

            # محاولة رفع الصورة إلى Supabase Storage
            public_url = upload_image_to_storage(response.content, filename, bucket_name)

            if public_url:
                logger.info(f"تم رفع الصورة بنجاح إلى Supabase: {public_url}")
                return public_url
            else:
                logger.warning("فشل في رفع الصورة إلى Supabase، سيتم رفعها إلى الموقع المستضاف")
                # رفع إلى الموقع المستضاف كحل بديل
                hosted_url = upload_image_to_hosting(response.content, filename)
                if hosted_url:
                    return hosted_url
                else:
                    logger.warning("فشل في رفع الصورة إلى الموقع المستضاف، سيتم الحفظ محلي<|im_start|>اً")
                    # حفظ محلي كحل أخير
                    local_path = save_image_locally(response.content, filename)
                    return local_path
        else:
            logger.error(f"فشل في تحميل الصورة من تيليجرام: {response.status_code}")
            return None

    except Exception as e:
        logger.error(f"خطأ في رفع صورة من تيليجرام: {e}")
        return None
